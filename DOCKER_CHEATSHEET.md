# 🐳 Docker EcoTask - Aide-mémoire

## 🚀 Commandes de base

### Démarrage/Arrêt
```bash
# Démarrer tous les services
cd /home/<USER>/roo/M1/fullStack/EcoTask && make quick-start
sudo docker-compose -f docker-compose.test.yml up -d

# Arrêter tous les services
sudo docker-compose -f docker-compose.test.yml down

cd /home/<USER>/roo/M1/fullStack/EcoTask && sudo docker-compose -f docker-compose.test.yml down --remove-orphans

# Redémarrer un service spécifique
sudo docker-compose -f docker-compose.test.yml restart app

# Voir l'état des services
sudo docker-compose -f docker-compose.test.yml ps
```

### Logs et monitoring
```bash
# Voir tous les logs
sudo docker-compose -f docker-compose.test.yml logs

# Suivre les logs en temps réel
sudo docker-compose -f docker-compose.test.yml logs -f

# Logs d'un service spécifique
sudo docker-compose -f docker-compose.test.yml logs app

# Statistiques des conteneurs
sudo docker stats
```

### Accès aux conteneurs
```bash
# Shell dans le conteneur app
sudo docker exec -it ecotask_app_test sh

# Shell dans le conteneur base de données
sudo docker exec -it ecotask_db_test bash

# Exécuter une commande Symfony
sudo docker exec ecotask_app_test php bin/console cache:clear
```

## 🗄️ Base de données

### Gestion Doctrine
```bash
# Créer la base de données
sudo docker exec ecotask_app_test php bin/console doctrine:database:create

# Exécuter les migrations
sudo docker exec ecotask_app_test php bin/console doctrine:migrations:migrate --no-interaction

# Charger les fixtures
sudo docker exec ecotask_app_test php bin/console doctrine:fixtures:load --no-interaction

# Réinitialiser complètement la base
sudo docker exec ecotask_app_test php bin/console doctrine:database:drop --force --if-exists
sudo docker exec ecotask_app_test php bin/console doctrine:database:create
sudo docker exec ecotask_app_test php bin/console doctrine:migrations:migrate --no-interaction
sudo docker exec ecotask_app_test php bin/console doctrine:fixtures:load --no-interaction
```

### Accès direct MySQL
```bash
# Connexion à MySQL
sudo docker exec -it ecotask_db_test mysql -u ecotask -pecotask_password ecotask_db

# Exporter la base
sudo docker exec ecotask_db_test mysqldump -u ecotask -pecotask_password ecotask_db > backup.sql

# Importer une base
sudo docker exec -i ecotask_db_test mysql -u ecotask -pecotask_password ecotask_db < backup.sql
```

## 🔧 Développement

### Tests et qualité
```bash
# Exécuter les tests
sudo docker exec ecotask_app_test php bin/phpunit

# Vérifier le style de code
sudo docker exec ecotask_app_test vendor/bin/php-cs-fixer fix --dry-run

# Analyse statique
sudo docker exec ecotask_app_test vendor/bin/phpstan analyse
```

### Cache et performance
```bash
# Vider le cache
sudo docker exec ecotask_app_test php bin/console cache:clear

# Réchauffer le cache
sudo docker exec ecotask_app_test php bin/console cache:warmup

# Voir les routes
sudo docker exec ecotask_app_test php bin/console debug:router
```

## 🌐 URLs importantes

- **Application** : http://localhost:8080
- **API Santé** : http://localhost:8080/health
- **Adminer** : http://localhost:8081
- **MailHog** : http://localhost:8025

## 🔍 Diagnostic

### Vérifications rapides
```bash
# Test de connectivité
curl http://localhost:8080/health

# Vérifier les ports
sudo netstat -tlnp | grep :8080

# Voir les processus Docker
sudo docker ps -a

# Inspecter un conteneur
sudo docker inspect ecotask_app_test
```

### Résolution de problèmes
```bash
# Reconstruire l'image
sudo docker-compose -f docker-compose.test.yml build --no-cache app

# Nettoyer les images inutilisées
sudo docker image prune -f

# Nettoyer complètement
sudo docker system prune -af

# Redémarrage complet
sudo docker-compose -f docker-compose.test.yml down -v
sudo docker-compose -f docker-compose.test.yml up -d --build
```

## 📦 Gestion des volumes

```bash
# Lister les volumes
sudo docker volume ls

# Inspecter un volume
sudo docker volume inspect ecotask_db_data

# Sauvegarder un volume
sudo docker run --rm -v ecotask_db_data:/data -v $(pwd):/backup alpine tar czf /backup/db_backup.tar.gz -C /data .

# Restaurer un volume
sudo docker run --rm -v ecotask_db_data:/data -v $(pwd):/backup alpine tar xzf /backup/db_backup.tar.gz -C /data
```

## 🚨 Urgences

### Application ne répond pas
```bash
# 1. Vérifier l'état
sudo docker-compose -f docker-compose.test.yml ps

# 2. Voir les logs
sudo docker-compose -f docker-compose.test.yml logs app

# 3. Redémarrer l'app
sudo docker-compose -f docker-compose.test.yml restart app

# 4. Si ça ne marche pas, redémarrage complet
sudo docker-compose -f docker-compose.test.yml down
sudo docker-compose -f docker-compose.test.yml up -d
```

### Base de données corrompue
```bash
# 1. Sauvegarder si possible
sudo docker exec ecotask_db_test mysqldump -u ecotask -pecotask_password ecotask_db > emergency_backup.sql

# 2. Recréer la base
sudo docker-compose -f docker-compose.test.yml down
sudo docker volume rm ecotask_db_data
sudo docker-compose -f docker-compose.test.yml up -d
sudo docker exec ecotask_app_test php bin/console doctrine:database:create
sudo docker exec ecotask_app_test php bin/console doctrine:migrations:migrate --no-interaction
sudo docker exec ecotask_app_test php bin/console doctrine:fixtures:load --no-interaction
```

### Espace disque plein
```bash
# Nettoyer Docker
sudo docker system prune -af --volumes

# Voir l'utilisation
sudo docker system df

# Supprimer les images inutilisées
sudo docker image prune -af
```

## 📋 Checklist quotidienne

### Démarrage de journée
- [ ] `sudo docker-compose -f docker-compose.test.yml up -d`
- [ ] `curl http://localhost:8080/health`
- [ ] Vérifier les logs : `sudo docker-compose -f docker-compose.test.yml logs`

### Fin de journée
- [ ] Sauvegarder si nécessaire
- [ ] `sudo docker-compose -f docker-compose.test.yml down`

### Avant un commit
- [ ] Tests : `sudo docker exec ecotask_app_test php bin/phpunit`
- [ ] Style : `sudo docker exec ecotask_app_test vendor/bin/php-cs-fixer fix`
- [ ] Santé : `curl http://localhost:8080/health`

---

*Gardez ce fichier à portée de main pour un accès rapide aux commandes essentielles !*
