2025-06-10 13:11:50,305 CRIT Supervisor is running as root.  Privileges were not dropped because no user is specified in the config file.  If you intend to run as root, you can set user=root in the config file to avoid this message.
2025-06-10 13:11:50,307 INFO supervisord started with pid 1
2025-06-10 13:11:51,309 INFO spawned: 'nginx' with pid 15
2025-06-10 13:11:51,310 INFO spawned: 'php-fpm' with pid 16
2025-06-10 13:11:52,337 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-06-10 13:11:52,338 INFO success: php-fpm entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-06-10 13:48:26,550 WARN received SIGQUIT indicating exit request
2025-06-10 13:48:26,550 INFO waiting for nginx, php-fpm to die
2025-06-10 13:48:26,559 INFO stopped: php-fpm (exit status 0)
2025-06-10 13:48:26,589 INFO stopped: nginx (exit status 0)
2025-06-10 13:48:32,374 CRIT Supervisor is running as root.  Privileges were not dropped because no user is specified in the config file.  If you intend to run as root, you can set user=root in the config file to avoid this message.
2025-06-10 13:48:32,378 INFO supervisord started with pid 1
2025-06-10 13:48:33,379 INFO spawned: 'nginx' with pid 15
2025-06-10 13:48:33,381 INFO spawned: 'php-fpm' with pid 16
2025-06-10 13:48:34,410 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-06-10 13:48:34,410 INFO success: php-fpm entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-06-10 14:59:43,152 WARN received SIGQUIT indicating exit request
2025-06-10 14:59:43,157 INFO waiting for nginx, php-fpm to die
2025-06-10 14:59:43,169 INFO stopped: php-fpm (exit status 0)
2025-06-10 14:59:43,203 INFO stopped: nginx (exit status 0)
2025-06-11 21:06:33,862 CRIT Supervisor is running as root.  Privileges were not dropped because no user is specified in the config file.  If you intend to run as root, you can set user=root in the config file to avoid this message.
2025-06-11 21:06:33,866 INFO supervisord started with pid 1
2025-06-11 21:06:34,872 INFO spawned: 'nginx' with pid 7
2025-06-11 21:06:34,874 INFO spawned: 'php-fpm' with pid 8
2025-06-11 21:06:35,941 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-06-11 21:06:35,941 INFO success: php-fpm entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-06-11 22:01:43,501 WARN received SIGQUIT indicating exit request
2025-06-11 22:01:43,501 INFO waiting for nginx, php-fpm to die
2025-06-11 22:01:43,513 INFO stopped: php-fpm (exit status 0)
2025-06-11 22:01:43,551 INFO stopped: nginx (exit status 0)
2025-06-14 16:36:19,584 CRIT Supervisor is running as root.  Privileges were not dropped because no user is specified in the config file.  If you intend to run as root, you can set user=root in the config file to avoid this message.
2025-06-14 16:36:19,587 INFO supervisord started with pid 1
2025-06-14 16:36:20,594 INFO spawned: 'nginx' with pid 7
2025-06-14 16:36:20,596 INFO spawned: 'php-fpm' with pid 8
2025-06-14 16:36:21,660 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-06-14 16:36:21,660 INFO success: php-fpm entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-06-15 13:38:39,095 WARN received SIGQUIT indicating exit request
2025-06-15 13:38:39,104 INFO waiting for nginx, php-fpm to die
2025-06-15 13:38:39,165 INFO stopped: php-fpm (exit status 0)
2025-06-15 13:38:39,223 INFO stopped: nginx (exit status 0)
2025-06-23 09:06:28,836 CRIT Supervisor is running as root.  Privileges were not dropped because no user is specified in the config file.  If you intend to run as root, you can set user=root in the config file to avoid this message.
2025-06-23 09:06:28,839 INFO supervisord started with pid 1
2025-06-23 09:06:29,847 INFO spawned: 'nginx' with pid 7
2025-06-23 09:06:29,850 INFO spawned: 'php-fpm' with pid 8
2025-06-23 09:06:30,920 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-06-23 09:06:30,920 INFO success: php-fpm entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-06-23 15:07:01,265 WARN received SIGQUIT indicating exit request
2025-06-23 15:07:01,281 INFO waiting for nginx, php-fpm to die
2025-06-23 15:07:01,371 INFO stopped: php-fpm (exit status 0)
2025-06-23 15:07:01,447 INFO stopped: nginx (exit status 0)
2025-06-24 06:58:32,834 CRIT Supervisor is running as root.  Privileges were not dropped because no user is specified in the config file.  If you intend to run as root, you can set user=root in the config file to avoid this message.
2025-06-24 06:58:32,837 INFO supervisord started with pid 1
2025-06-24 06:58:33,845 INFO spawned: 'nginx' with pid 7
2025-06-24 06:58:33,847 INFO spawned: 'php-fpm' with pid 8
2025-06-24 06:58:34,919 INFO success: nginx entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
2025-06-24 06:58:34,920 INFO success: php-fpm entered RUNNING state, process has stayed up for > than 1 seconds (startsecs)
