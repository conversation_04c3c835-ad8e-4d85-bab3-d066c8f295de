<?php

namespace App\DataFixtures;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\User;
use DateTimeImmutable;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class AppFixtures extends Fixture
{
    public function __construct(
        private UserPasswordHasherInterface $passwordHasher,
    ) {
    }

    public function load(ObjectManager $manager): void
    {
        // Créer des utilisateurs
        $users = [];

        $user1 = new User();
        $user1->setEmail('<EMAIL>');
        $user1->setFirstName('Alice');
        $user1->setLastName('Martin');
        $user1->setPassword($this->passwordHasher->hashPassword($user1, 'password'));
        $manager->persist($user1);
        $users[] = $user1;

        $user2 = new User();
        $user2->setEmail('<EMAIL>');
        $user2->setFirstName('Bob');
        $user2->setLastName('Dupont');
        $user2->setPassword($this->passwordHasher->hashPassword($user2, 'password'));
        $manager->persist($user2);
        $users[] = $user2;

        $user3 = new User();
        $user3->setEmail('<EMAIL>');
        $user3->setFirstName('Charlie');
        $user3->setLastName('Durand');
        $user3->setPassword($this->passwordHasher->hashPassword($user3, 'password'));
        $manager->persist($user3);
        $users[] = $user3;

        // Créer des projets
        $projects = [];

        $project1 = new Project();
        $project1->setName('Site Web Éco-responsable');
        $project1->setDescription('Développement d\'un site web avec une approche éco-responsable pour réduire l\'empreinte carbone.');
        $project1->addMember($user1);
        $project1->addMember($user2);
        $manager->persist($project1);
        $projects[] = $project1;

        $project2 = new Project();
        $project2->setName('Application Mobile Verte');
        $project2->setDescription('Création d\'une application mobile pour le suivi de l\'empreinte carbone personnelle.');
        $project2->addMember($user2);
        $project2->addMember($user3);
        $manager->persist($project2);
        $projects[] = $project2;

        $project3 = new Project();
        $project3->setName('Optimisation Énergétique');
        $project3->setDescription('Projet d\'optimisation de la consommation énergétique des serveurs.');
        $project3->addMember($user1);
        $project3->addMember($user3);
        $manager->persist($project3);
        $projects[] = $project3;

        // Créer des tâches
        $tasks = [
            // Projet 1 - Site Web Éco-responsable
            [
                'title' => 'Analyse des besoins',
                'description' => 'Analyser les besoins fonctionnels et techniques du site web.',
                'type' => Task::TYPE_OFFICE_LIGHT,
                'priority' => Task::PRIORITY_HIGH,
                'status' => Task::STATUS_DONE,
                'estimatedHours' => '8',
                'actualHours' => '7.5',
                'project' => $project1,
                'assignedTo' => $user1,
                'dueDate' => new DateTimeImmutable('-5 days'),
            ],
            [
                'title' => 'Conception de l\'architecture',
                'description' => 'Concevoir l\'architecture technique du site web.',
                'type' => Task::TYPE_TECHNICAL,
                'priority' => Task::PRIORITY_HIGH,
                'status' => Task::STATUS_DONE,
                'estimatedHours' => '12',
                'actualHours' => '14',
                'project' => $project1,
                'assignedTo' => $user2,
                'dueDate' => new DateTimeImmutable('-3 days'),
            ],
            [
                'title' => 'Développement du frontend',
                'description' => 'Développer l\'interface utilisateur avec React.',
                'type' => Task::TYPE_TECHNICAL,
                'priority' => Task::PRIORITY_MEDIUM,
                'status' => Task::STATUS_IN_PROGRESS,
                'estimatedHours' => '40',
                'actualHours' => '25',
                'project' => $project1,
                'assignedTo' => $user1,
                'dueDate' => new DateTimeImmutable('+5 days'),
            ],
            [
                'title' => 'Tests de performance',
                'description' => 'Effectuer des tests de performance et d\'optimisation.',
                'type' => Task::TYPE_ENERGY_INTENSIVE,
                'priority' => Task::PRIORITY_MEDIUM,
                'status' => Task::STATUS_TODO,
                'estimatedHours' => '16',
                'project' => $project1,
                'assignedTo' => $user2,
                'dueDate' => new DateTimeImmutable('+10 days'),
            ],

            // Projet 2 - Application Mobile Verte
            [
                'title' => 'Étude de marché',
                'description' => 'Recherche sur les applications existantes et analyse concurrentielle.',
                'type' => Task::TYPE_OFFICE_LIGHT,
                'priority' => Task::PRIORITY_MEDIUM,
                'status' => Task::STATUS_DONE,
                'estimatedHours' => '6',
                'actualHours' => '5.5',
                'project' => $project2,
                'assignedTo' => $user3,
                'dueDate' => new DateTimeImmutable('-7 days'),
            ],
            [
                'title' => 'Développement API',
                'description' => 'Développer l\'API REST pour l\'application mobile.',
                'type' => Task::TYPE_TECHNICAL,
                'priority' => Task::PRIORITY_HIGH,
                'status' => Task::STATUS_IN_PROGRESS,
                'estimatedHours' => '30',
                'actualHours' => '18',
                'project' => $project2,
                'assignedTo' => $user2,
                'dueDate' => new DateTimeImmutable('+3 days'),
            ],
            [
                'title' => 'Interface mobile',
                'description' => 'Créer l\'interface utilisateur de l\'application mobile.',
                'type' => Task::TYPE_TECHNICAL,
                'priority' => Task::PRIORITY_MEDIUM,
                'status' => Task::STATUS_TODO,
                'estimatedHours' => '35',
                'project' => $project2,
                'assignedTo' => $user3,
                'dueDate' => new DateTimeImmutable('+15 days'),
            ],

            // Projet 3 - Optimisation Énergétique
            [
                'title' => 'Audit énergétique',
                'description' => 'Audit de la consommation énergétique actuelle des serveurs.',
                'type' => Task::TYPE_OFFICE_LIGHT,
                'priority' => Task::PRIORITY_HIGH,
                'status' => Task::STATUS_DONE,
                'estimatedHours' => '10',
                'actualHours' => '12',
                'project' => $project3,
                'assignedTo' => $user1,
                'dueDate' => new DateTimeImmutable('-2 days'),
            ],
            [
                'title' => 'Simulation d\'optimisation',
                'description' => 'Simuler différents scénarios d\'optimisation énergétique.',
                'type' => Task::TYPE_ENERGY_INTENSIVE,
                'priority' => Task::PRIORITY_HIGH,
                'status' => Task::STATUS_IN_PROGRESS,
                'estimatedHours' => '20',
                'actualHours' => '8',
                'project' => $project3,
                'assignedTo' => $user3,
                'dueDate' => new DateTimeImmutable('+2 days'),
            ],
            [
                'title' => 'Rapport final',
                'description' => 'Rédiger le rapport final avec les recommandations.',
                'type' => Task::TYPE_OFFICE_LIGHT,
                'priority' => Task::PRIORITY_MEDIUM,
                'status' => Task::STATUS_TODO,
                'estimatedHours' => '8',
                'project' => $project3,
                'assignedTo' => $user1,
                'dueDate' => new DateTimeImmutable('+20 days'),
            ],
        ];

        foreach ($tasks as $taskData) {
            $task = new Task();
            $task->setTitle($taskData['title']);
            $task->setDescription($taskData['description']);
            $task->setType($taskData['type']);
            $task->setPriority($taskData['priority']);
            $task->setStatus($taskData['status']);
            $task->setEstimatedHours($taskData['estimatedHours']);
            $task->setProject($taskData['project']);
            $task->setAssignedTo($taskData['assignedTo']);
            $task->setDueDate($taskData['dueDate']);

            if (isset($taskData['actualHours'])) {
                $task->setActualHours($taskData['actualHours']);
            }

            if ($taskData['status'] === Task::STATUS_DONE) {
                $task->setCompletedAt(new DateTimeImmutable('-1 day'));
            }

            $manager->persist($task);
        }

        $manager->flush();
    }
}
