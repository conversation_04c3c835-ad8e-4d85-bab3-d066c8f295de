#!/bin/bash

# Script de déploiement EcoTask
# Usage: ./scripts/deploy.sh [environment]

set -e

ENVIRONMENT=${1:-development}
PROJECT_NAME="ecotask"

echo "🚀 Déploiement EcoTask - Environnement: $ENVIRONMENT"

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification des prérequis
check_requirements() {
    log_info "Vérification des prérequis..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker n'est pas installé"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose n'est pas installé"
        exit 1
    fi
    
    log_success "Prérequis OK"
}

# Configuration de l'environnement
setup_environment() {
    log_info "Configuration de l'environnement $ENVIRONMENT..."
    
    if [ "$ENVIRONMENT" = "production" ]; then
        if [ ! -f ".env.prod" ]; then
            log_error "Fichier .env.prod manquant"
            exit 1
        fi
        cp .env.prod .env
        COMPOSE_FILE="docker-compose.prod.yml"
    else
        if [ ! -f ".env" ]; then
            log_warning "Fichier .env manquant, copie depuis .env.example"
            cp .env.example .env
        fi
        COMPOSE_FILE="docker-compose.yml"
    fi
    
    log_success "Environnement configuré"
}

# Construction des images Docker
build_images() {
    log_info "Construction des images Docker..."
    
    docker-compose -f $COMPOSE_FILE build --no-cache
    
    log_success "Images construites"
}

# Démarrage des services
start_services() {
    log_info "Démarrage des services..."
    
    docker-compose -f $COMPOSE_FILE up -d
    
    # Attendre que les services soient prêts
    log_info "Attente du démarrage des services..."
    sleep 30
    
    log_success "Services démarrés"
}

# Installation des dépendances
install_dependencies() {
    log_info "Installation des dépendances..."
    
    # Composer
    docker-compose -f $COMPOSE_FILE exec app composer install --no-dev --optimize-autoloader
    
    # NPM (si nécessaire)
    if [ -f "package.json" ]; then
        docker-compose -f $COMPOSE_FILE run --rm node npm ci
        docker-compose -f $COMPOSE_FILE run --rm node npm run build
    fi
    
    log_success "Dépendances installées"
}

# Configuration de la base de données
setup_database() {
    log_info "Configuration de la base de données..."
    
    # Attendre que la base soit prête
    docker-compose -f $COMPOSE_FILE exec app php bin/console doctrine:database:create --if-not-exists
    
    # Migrations
    docker-compose -f $COMPOSE_FILE exec app php bin/console doctrine:migrations:migrate --no-interaction
    
    # Fixtures (seulement en développement)
    if [ "$ENVIRONMENT" != "production" ]; then
        docker-compose -f $COMPOSE_FILE exec app php bin/console doctrine:fixtures:load --no-interaction
    fi
    
    log_success "Base de données configurée"
}

# Optimisation pour la production
optimize_production() {
    if [ "$ENVIRONMENT" = "production" ]; then
        log_info "Optimisations pour la production..."
        
        # Cache Symfony
        docker-compose -f $COMPOSE_FILE exec app php bin/console cache:clear --env=prod
        docker-compose -f $COMPOSE_FILE exec app php bin/console cache:warmup --env=prod
        
        # Assets
        docker-compose -f $COMPOSE_FILE exec app php bin/console assets:install --env=prod
        
        log_success "Optimisations appliquées"
    fi
}

# Tests de santé
health_check() {
    log_info "Vérification de la santé de l'application..."
    
    # Test HTTP
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        log_success "Application accessible"
    else
        log_error "Application non accessible"
        exit 1
    fi
    
    # Test base de données
    if docker-compose -f $COMPOSE_FILE exec app php bin/console doctrine:query:sql "SELECT 1" > /dev/null 2>&1; then
        log_success "Base de données accessible"
    else
        log_error "Base de données non accessible"
        exit 1
    fi
}

# Nettoyage
cleanup() {
    log_info "Nettoyage..."
    
    # Suppression des images non utilisées
    docker image prune -f
    
    # Suppression des volumes orphelins
    docker volume prune -f
    
    log_success "Nettoyage terminé"
}

# Fonction principale
main() {
    echo "🌱 EcoTask Deployment Script"
    echo "================================"
    
    check_requirements
    setup_environment
    build_images
    start_services
    install_dependencies
    setup_database
    optimize_production
    health_check
    cleanup
    
    echo ""
    log_success "🎉 Déploiement terminé avec succès!"
    echo ""
    echo "📊 Services disponibles:"
    echo "  - Application: http://localhost:8080"
    echo "  - Adminer: http://localhost:8081"
    if [ "$ENVIRONMENT" != "production" ]; then
        echo "  - MailHog: http://localhost:8025"
    else
        echo "  - Grafana: http://localhost:3000"
        echo "  - Prometheus: http://localhost:9090"
    fi
    echo ""
}

# Gestion des signaux
trap 'log_error "Déploiement interrompu"; exit 1' INT TERM

# Exécution
main "$@"
