#!/bin/bash

# Script de validation complète CI/CD pour EcoTask
# Usage: ./scripts/validate-cicd.sh

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier la structure des workflows GitHub Actions
check_github_workflows() {
    log_info "Vérification des workflows GitHub Actions..."
    
    local workflows_dir=".github/workflows"
    local required_workflows=("ci.yml" "deploy.yml" "monitoring.yml")
    local missing_workflows=0
    
    if [ ! -d "$workflows_dir" ]; then
        log_error "Répertoire $workflows_dir manquant"
        return 1
    fi
    
    for workflow in "${required_workflows[@]}"; do
        if [ ! -f "$workflows_dir/$workflow" ]; then
            log_error "Workflow manquant: $workflow"
            ((missing_workflows++))
        else
            log_success "Workflow trouvé: $workflow"
        fi
    done
    
    if [ $missing_workflows -gt 0 ]; then
        log_error "$missing_workflows workflow(s) manquant(s)"
        return 1
    fi
    
    log_success "Tous les workflows GitHub Actions sont présents"
    return 0
}

# Vérifier les outils de qualité
check_quality_tools() {
    log_info "Vérification des outils de qualité..."
    
    local tools_config=(
        ".php-cs-fixer.dist.php:PHP CS Fixer"
        "phpstan.neon:PHPStan"
        "phpunit.dist.xml:PHPUnit"
    )
    
    local missing_configs=0
    
    for tool_config in "${tools_config[@]}"; do
        local file=$(echo "$tool_config" | cut -d: -f1)
        local name=$(echo "$tool_config" | cut -d: -f2)
        
        if [ ! -f "$file" ]; then
            log_error "Configuration manquante: $name ($file)"
            ((missing_configs++))
        else
            log_success "Configuration trouvée: $name"
        fi
    done
    
    if [ $missing_configs -gt 0 ]; then
        log_error "$missing_configs configuration(s) manquante(s)"
        return 1
    fi
    
    log_success "Toutes les configurations d'outils sont présentes"
    return 0
}

# Vérifier les dépendances Composer
check_composer_dependencies() {
    log_info "Vérification des dépendances Composer..."
    
    local required_dev_deps=(
        "friendsofphp/php-cs-fixer"
        "phpstan/phpstan"
        "phpstan/phpstan-symfony"
        "phpstan/phpstan-doctrine"
        "phpunit/phpunit"
    )
    
    local missing_deps=0
    
    for dep in "${required_dev_deps[@]}"; do
        if ! grep -q "\"$dep\"" composer.json; then
            log_error "Dépendance manquante: $dep"
            ((missing_deps++))
        else
            log_success "Dépendance trouvée: $dep"
        fi
    done
    
    if [ $missing_deps -gt 0 ]; then
        log_error "$missing_deps dépendance(s) manquante(s)"
        return 1
    fi
    
    log_success "Toutes les dépendances de développement sont présentes"
    return 0
}

# Vérifier les scripts Makefile
check_makefile_targets() {
    log_info "Vérification des cibles Makefile..."
    
    local required_targets=(
        "ci-pipeline"
        "pre-commit"
        "cs-check"
        "cs-fix"
        "phpstan"
        "test"
        "security-check"
    )
    
    local missing_targets=0
    
    if [ ! -f "Makefile" ]; then
        log_error "Makefile manquant"
        return 1
    fi
    
    for target in "${required_targets[@]}"; do
        if ! grep -q "^$target:" Makefile; then
            log_error "Cible Makefile manquante: $target"
            ((missing_targets++))
        else
            log_success "Cible Makefile trouvée: $target"
        fi
    done
    
    if [ $missing_targets -gt 0 ]; then
        log_error "$missing_targets cible(s) Makefile manquante(s)"
        return 1
    fi
    
    log_success "Toutes les cibles Makefile sont présentes"
    return 0
}

# Vérifier les configurations Docker
check_docker_configs() {
    log_info "Vérification des configurations Docker..."
    
    local docker_files=(
        "Dockerfile:Image principale"
        "Dockerfile.test:Image de test"
        "docker-compose.yml:Développement"
        "docker-compose.test.yml:Tests"
        "docker-compose.prod.yml:Production"
    )
    
    local missing_files=0
    
    for docker_file in "${docker_files[@]}"; do
        local file=$(echo "$docker_file" | cut -d: -f1)
        local name=$(echo "$docker_file" | cut -d: -f2)
        
        if [ ! -f "$file" ]; then
            log_error "Fichier Docker manquant: $name ($file)"
            ((missing_files++))
        else
            log_success "Fichier Docker trouvé: $name"
        fi
    done
    
    if [ $missing_files -gt 0 ]; then
        log_error "$missing_files fichier(s) Docker manquant(s)"
        return 1
    fi
    
    log_success "Toutes les configurations Docker sont présentes"
    return 0
}

# Vérifier les scripts d'automatisation
check_automation_scripts() {
    log_info "Vérification des scripts d'automatisation..."
    
    local scripts=(
        "scripts/deploy.sh:Déploiement"
        "scripts/cleanup-docker.sh:Nettoyage Docker"
        "scripts/cleanup-codebase.sh:Nettoyage code"
        "scripts/init-database.sh:Initialisation DB"
        "scripts/test-docker.sh:Tests Docker"
        "scripts/pre-commit.sh:Pre-commit hook"
    )
    
    local missing_scripts=0
    
    for script in "${scripts[@]}"; do
        local file=$(echo "$script" | cut -d: -f1)
        local name=$(echo "$script" | cut -d: -f2)
        
        if [ ! -f "$file" ]; then
            log_error "Script manquant: $name ($file)"
            ((missing_scripts++))
        elif [ ! -x "$file" ]; then
            log_warning "Script non exécutable: $name ($file)"
            chmod +x "$file"
            log_success "Permissions corrigées: $name"
        else
            log_success "Script trouvé: $name"
        fi
    done
    
    if [ $missing_scripts -gt 0 ]; then
        log_error "$missing_scripts script(s) manquant(s)"
        return 1
    fi
    
    log_success "Tous les scripts d'automatisation sont présents"
    return 0
}

# Tester le pipeline CI local
test_local_ci_pipeline() {
    log_info "Test du pipeline CI local..."
    
    # Vérifier que Docker est disponible
    if ! docker ps >/dev/null 2>&1; then
        log_warning "Docker non disponible, test du pipeline ignoré"
        return 0
    fi
    
    # Vérifier que l'environnement est démarré
    if ! docker ps | grep -q "ecotask_app"; then
        log_info "Démarrage de l'environnement de test..."
        make quick-start >/dev/null 2>&1
    fi
    
    # Exécuter le pipeline CI
    if make ci-pipeline >/dev/null 2>&1; then
        log_success "Pipeline CI local réussi"
        return 0
    else
        log_error "Pipeline CI local échoué"
        log_info "Exécutez 'make ci-pipeline' pour voir les détails"
        return 1
    fi
}

# Vérifier la documentation
check_documentation() {
    log_info "Vérification de la documentation..."
    
    local docs=(
        "README.md:Documentation principale"
        "CI_CD_README.md:Documentation CI/CD"
        "DOCKER_GUIDE.md:Guide Docker"
        "DOCKER_CHEATSHEET.md:Aide-mémoire Docker"
    )
    
    local missing_docs=0
    
    for doc in "${docs[@]}"; do
        local file=$(echo "$doc" | cut -d: -f1)
        local name=$(echo "$doc" | cut -d: -f2)
        
        if [ ! -f "$file" ]; then
            log_error "Documentation manquante: $name ($file)"
            ((missing_docs++))
        else
            log_success "Documentation trouvée: $name"
        fi
    done
    
    if [ $missing_docs -gt 0 ]; then
        log_error "$missing_docs document(s) manquant(s)"
        return 1
    fi
    
    log_success "Toute la documentation est présente"
    return 0
}

# Générer un rapport de validation
generate_report() {
    local total_checks=$1
    local failed_checks=$2
    local passed_checks=$((total_checks - failed_checks))
    
    echo ""
    echo "📊 Rapport de Validation CI/CD"
    echo "==============================="
    echo ""
    echo "✅ Vérifications réussies: $passed_checks"
    echo "❌ Vérifications échouées: $failed_checks"
    echo "📈 Taux de réussite: $(( passed_checks * 100 / total_checks ))%"
    echo ""
    
    if [ $failed_checks -eq 0 ]; then
        echo "🎉 Configuration CI/CD complète et fonctionnelle !"
        echo ""
        echo "🚀 Prochaines étapes:"
        echo "  1. Commitez vos changements"
        echo "  2. Poussez vers GitHub"
        echo "  3. Vérifiez que les workflows se déclenchent"
        echo "  4. Configurez les secrets GitHub si nécessaire"
        echo ""
    else
        echo "⚠️  Des améliorations sont nécessaires"
        echo ""
        echo "🔧 Actions recommandées:"
        echo "  1. Corrigez les erreurs signalées"
        echo "  2. Re-exécutez ce script"
        echo "  3. Testez le pipeline local: make ci-pipeline"
        echo ""
    fi
}

# Fonction principale
main() {
    echo "🔍 Validation CI/CD EcoTask"
    echo "============================"
    echo ""
    
    local failed_checks=0
    local total_checks=0
    
    # Exécution de toutes les vérifications
    ((total_checks++))
    check_github_workflows || ((failed_checks++))
    
    ((total_checks++))
    check_quality_tools || ((failed_checks++))
    
    ((total_checks++))
    check_composer_dependencies || ((failed_checks++))
    
    ((total_checks++))
    check_makefile_targets || ((failed_checks++))
    
    ((total_checks++))
    check_docker_configs || ((failed_checks++))
    
    ((total_checks++))
    check_automation_scripts || ((failed_checks++))
    
    ((total_checks++))
    check_documentation || ((failed_checks++))
    
    ((total_checks++))
    test_local_ci_pipeline || ((failed_checks++))
    
    # Génération du rapport
    generate_report $total_checks $failed_checks
    
    # Code de sortie
    if [ $failed_checks -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# Gestion des signaux
trap 'log_error "Validation interrompue"; exit 1' INT TERM

# Exécution
main "$@"
