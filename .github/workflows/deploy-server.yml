name: 🚀 Deploy to Server

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
    types: [ closed ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  PHP_VERSION: '8.3'

jobs:
  # Job 1: Deploy to Staging Server
  deploy-staging:
    name: 🎭 Deploy Staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'staging'
    environment:
      name: staging
      url: ${{ secrets.STAGING_URL }}

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🚀 Deploy to Staging Server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: ${{ secrets.SERVER_PORT || 22 }}
          script: |
            echo "🎭 Déploiement staging sur serveur..."
            
            # Aller dans le répertoire de staging
            cd ${{ secrets.SERVER_PATH }}/staging || exit 1
            
            # Sauvegarder la version actuelle
            cp -r . ../backup-staging-$(date +%Y%m%d-%H%M%S) || true
            
            # Mettre à jour le code
            git fetch origin
            git reset --hard origin/main
            
            # Installer les dépendances
            composer install --no-dev --optimize-autoloader --no-interaction
            
            # Mettre à jour la base de données
            php bin/console doctrine:migrations:migrate --no-interaction --env=prod
            
            # Vider le cache
            php bin/console cache:clear --env=prod
            php bin/console cache:warmup --env=prod
            
            # Ajuster les permissions
            chown -R www-data:www-data var/
            chmod -R 775 var/
            
            # Recharger les services
            sudo systemctl reload php8.3-fpm
            sudo systemctl reload nginx
            
            echo "✅ Déploiement staging terminé"

      - name: 🔍 Health Check Staging
        run: |
          echo "🔍 Vérification de santé du staging..."
          sleep 15
          
          # Attendre que le service soit prêt
          for i in {1..10}; do
            if curl -f -s "${{ secrets.STAGING_URL }}/health" > /dev/null; then
              echo "✅ Staging opérationnel (tentative $i)"
              break
            else
              echo "⏳ Attente du staging... (tentative $i/10)"
              sleep 10
            fi
          done
          
          # Vérification finale
          if curl -f -s "${{ secrets.STAGING_URL }}/health"; then
            echo "✅ Health check staging réussi"
          else
            echo "❌ Health check staging échoué"
            exit 1
          fi

      - name: 📢 Notification Staging
        if: always()
        run: |
          STATUS="${{ job.status }}"
          if [ "$STATUS" = "success" ]; then
            MESSAGE="✅ *Déploiement Staging Réussi*%0A%0A"
          else
            MESSAGE="❌ *Déploiement Staging Échoué*%0A%0A"
          fi
          
          MESSAGE="${MESSAGE}🌐 *URL*: ${{ secrets.STAGING_URL }}%0A"
          MESSAGE="${MESSAGE}🔧 *Branche*: ${{ github.ref_name }}%0A"
          MESSAGE="${MESSAGE}📝 *Commit*: ${{ github.sha }}%0A"
          MESSAGE="${MESSAGE}⏰ *Timestamp*: $(date -u)%0A"
          MESSAGE="${MESSAGE}🔗 *Workflow*: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          
          curl -s -X POST "https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage" \
            -d "chat_id=${{ secrets.TELEGRAM_CHAT_ID }}" \
            -d "text=${MESSAGE}" \
            -d "parse_mode=Markdown" \
            -d "disable_web_page_preview=true" || echo "Notification Telegram échouée"

  # Job 2: Deploy to Production Server
  deploy-production:
    name: 🏭 Deploy Production
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: startsWith(github.ref, 'refs/tags/v') || github.event.inputs.environment == 'production'
    environment:
      name: production
      url: ${{ secrets.PRODUCTION_URL }}

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔐 Production Safety Check
        run: |
          echo "🔐 Vérifications de sécurité production..."
          if [ -z "${{ secrets.PRODUCTION_URL }}" ]; then
            echo "❌ PRODUCTION_URL non configurée"
            exit 1
          fi
          echo "✅ Secrets de production vérifiés"

      - name: 🚀 Deploy to Production Server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: ${{ secrets.SERVER_PORT || 22 }}
          script: |
            echo "🏭 Déploiement production sur serveur..."
            
            # Aller dans le répertoire de production
            cd ${{ secrets.SERVER_PATH }}/production || exit 1
            
            # Sauvegarder la version actuelle
            cp -r . ../backup-production-$(date +%Y%m%d-%H%M%S) || true
            
            # Mettre à jour le code
            git fetch origin
            git reset --hard ${{ github.sha }}
            
            # Installer les dépendances
            composer install --no-dev --optimize-autoloader --no-interaction
            
            # Mettre à jour la base de données
            php bin/console doctrine:migrations:migrate --no-interaction --env=prod
            
            # Vider le cache
            php bin/console cache:clear --env=prod
            php bin/console cache:warmup --env=prod
            
            # Ajuster les permissions
            chown -R www-data:www-data var/
            chmod -R 775 var/
            
            # Recharger les services
            sudo systemctl reload php8.3-fpm
            sudo systemctl reload nginx
            
            echo "✅ Déploiement production terminé"

      - name: 🔍 Health Check Production
        run: |
          echo "🔍 Vérification de santé de la production..."
          sleep 15
          
          # Attendre que le service soit prêt
          for i in {1..10}; do
            if curl -f -s "${{ secrets.PRODUCTION_URL }}/health" > /dev/null; then
              echo "✅ Production opérationnelle (tentative $i)"
              break
            else
              echo "⏳ Attente de la production... (tentative $i/10)"
              sleep 10
            fi
          done
          
          # Vérification finale
          if curl -f -s "${{ secrets.PRODUCTION_URL }}/health"; then
            echo "✅ Health check production réussi"
          else
            echo "❌ Health check production échoué"
            exit 1
          fi

      - name: 📢 Notification Production
        if: always()
        run: |
          STATUS="${{ job.status }}"
          if [ "$STATUS" = "success" ]; then
            MESSAGE="🎉 *Déploiement Production Réussi*%0A%0A"
          else
            MESSAGE="🚨 *Déploiement Production Échoué*%0A%0A"
          fi
          
          MESSAGE="${MESSAGE}🌐 *URL*: ${{ secrets.PRODUCTION_URL }}%0A"
          MESSAGE="${MESSAGE}🏷️ *Version*: ${{ github.ref_name }}%0A"
          MESSAGE="${MESSAGE}📝 *Commit*: ${{ github.sha }}%0A"
          MESSAGE="${MESSAGE}⏰ *Timestamp*: $(date -u)%0A"
          MESSAGE="${MESSAGE}🔗 *Workflow*: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          
          curl -s -X POST "https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage" \
            -d "chat_id=${{ secrets.TELEGRAM_CHAT_ID }}" \
            -d "text=${MESSAGE}" \
            -d "parse_mode=Markdown" \
            -d "disable_web_page_preview=true" || echo "Notification Telegram échouée"

  # Job 3: Trigger Monitoring
  trigger-monitoring:
    name: 📊 Trigger Monitoring
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always() && (needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success')
    
    steps:
      - name: 📊 Trigger Monitoring Workflow
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.actions.createWorkflowDispatch({
              owner: context.repo.owner,
              repo: context.repo.repo,
              workflow_id: 'monitoring.yml',
              ref: 'main',
              inputs: {
                check_type: 'health'
              }
            });
            console.log('✅ Monitoring déclenché après déploiement');
