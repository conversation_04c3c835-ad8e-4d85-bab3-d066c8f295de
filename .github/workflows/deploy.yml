name: 🚀 Déploiement

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environnement de déploiement'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

permissions:
  contents: read
  packages: write
  actions: write

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Job 1: Build et Push des images Docker
  build-and-push:
    name: 🏗️ Build & Push Images
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 🏷️ Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix=sha-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: 🏗️ Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          target: production
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Job 2: Déploiement Staging
  deploy-staging:
    name: 🎭 Déploiement Staging
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'staging'
    environment:
      name: staging
      url: https://staging.ecotask.example.com

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🚀 Deploy to Staging
        run: |
          echo "🎭 Déploiement en staging..."
          echo "Image: ${{ needs.build-and-push.outputs.image-tag }}"

          # Ici, vous ajouteriez les commandes de déploiement réelles
          # Par exemple: kubectl, docker-compose, ou scripts de déploiement

          # Simulation du déploiement
          echo "✅ Déploiement staging simulé avec succès"

      - name: 🔍 Health Check Staging
        run: |
          echo "🔍 Vérification de santé du staging..."
          # curl -f https://staging.ecotask.example.com/health
          echo "✅ Staging opérationnel"

      - name: 📢 Notification Déploiement (Console)
        if: always()
        run: |
          echo "🎭 Déploiement Staging: ${{ job.status }}"
          echo "Branche: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          echo "Image: ${{ needs.build-and-push.outputs.image-tag }}"
          echo "✅ Notification envoyée"

  # Job 3: Déploiement Production (manuel ou tag)
  deploy-production:
    name: 🏭 Déploiement Production
    runs-on: ubuntu-latest
    needs: build-and-push
    if: startsWith(github.ref, 'refs/tags/v') || github.event.inputs.environment == 'production'
    environment:
      name: production
      url: https://ecotask.example.com

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔐 Setup Production Secrets
        run: |
          echo "🔐 Configuration des secrets de production..."
          # Ici, vous configureriez les secrets et variables d'environnement

      - name: 📋 Pre-deployment Checks
        run: |
          echo "📋 Vérifications pré-déploiement..."
          echo "✅ Base de données accessible"
          echo "✅ Secrets configurés"
          echo "✅ Monitoring opérationnel"

      - name: 🚀 Deploy to Production
        run: |
          echo "🏭 Déploiement en production..."
          echo "Image: ${{ needs.build-and-push.outputs.image-tag }}"

          # Commandes de déploiement production
          # chmod +x scripts/deploy.sh
          # ./scripts/deploy.sh production

          echo "✅ Déploiement production simulé avec succès"

      - name: 🔍 Health Check Production
        run: |
          echo "🔍 Vérification de santé de la production..."
          # curl -f https://ecotask.example.com/health
          echo "✅ Production opérationnelle"

      - name: 📊 Post-deployment Monitoring
        run: |
          echo "📊 Activation du monitoring post-déploiement..."
          echo "✅ Métriques activées"
          echo "✅ Alertes configurées"

  # Job 5: Post-deployment Summary
  post-deployment:
    name: 📊 Post-deployment Summary
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always() && (needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success')

    steps:
      - name: 📊 Deployment Summary
        run: |
          echo "📊 Résumé du déploiement"
          echo "======================="

          if [ "${{ needs.deploy-staging.result }}" = "success" ]; then
            echo "✅ Staging: Déployé avec succès"
          else
            echo "❌ Staging: Échec ou ignoré"
          fi

          if [ "${{ needs.deploy-production.result }}" = "success" ]; then
            echo "✅ Production: Déployé avec succès"
          else
            echo "❌ Production: Échec ou ignoré"
          fi

          echo ""
          echo "💡 Le monitoring s'exécutera automatiquement selon son planning."
          echo "   Pour un monitoring immédiat, allez dans Actions > Monitoring > Run workflow"

      - name: 📢 Notification Production (Console)
        if: always()
        run: |
          echo "🏭 Déploiement Production: ${{ job.status }}"
          echo "Version: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          echo "Image: ${{ needs.build-and-push.outputs.image-tag }}"
          echo "URL: https://ecotask.example.com"
          echo "✅ Notification envoyée"

  # Job 4: Rollback automatique en cas d'échec
  rollback:
    name: 🔄 Rollback
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: failure() && (needs.deploy-staging.result == 'failure' || needs.deploy-production.result == 'failure')

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔄 Automatic Rollback
        run: |
          echo "🔄 Rollback automatique en cours..."
          echo "⚠️ Échec détecté, retour à la version précédente"

          # Commandes de rollback
          # ./scripts/rollback.sh

          echo "✅ Rollback effectué"

      - name: 📢 Notification Rollback (Console)
        run: |
          echo "🔄 ROLLBACK AUTOMATIQUE EFFECTUÉ"
          echo "Raison: Échec du déploiement"
          echo "Branche: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          echo "⚠️ Alerte envoyée"
