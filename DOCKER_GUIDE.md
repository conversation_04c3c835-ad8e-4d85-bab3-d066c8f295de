# 🐳 Guide Complet de Conteneurisation EcoTask

## 📋 Table des matières
1. [Vue d'ensemble](#vue-densemble)
2. [Architecture Docker](#architecture-docker)
3. [Fichiers importants](#fichiers-importants)
4. [Fonctionnement détaillé](#fonctionnement-détaillé)
5. [Commandes essentielles](#commandes-essentielles)
6. [Workflows de développement](#workflows-de-développement)
7. [Dépannage](#dépannage)

---

## 🎯 Vue d'ensemble

### Qu'est-ce que notre conteneurisation ?
Notre conteneurisation EcoTask transforme l'application Symfony en **services isolés et orchestrés** qui communiquent entre eux dans un environnement Docker.

### Avantages obtenus :
- ✅ **Environnement reproductible** : Même configuration partout
- ✅ **Isolation des services** : Base de données, cache, application séparés
- ✅ **Scalabilité** : Possibilité d'ajouter des instances
- ✅ **Déploiement simplifié** : Un seul `docker-compose up`
- ✅ **Développement uniforme** : Même environnement pour toute l'équipe

---

## 🏗️ Architecture Docker

### Schéma de l'architecture :
```
┌─────────────────────────────────────────────────────────────┐
│                    RÉSEAU DOCKER                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │     APP     │  │   DATABASE  │  │    REDIS    │         │
│  │ PHP 8.3 +   │  │   MySQL 8.0 │  │   Cache +   │         │
│  │ Nginx +     │◄─┤             │  │   Sessions  │         │
│  │ Supervisor  │  │             │  │             │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│         │                                                   │
│  ┌─────────────┐  ┌─────────────┐                          │
│  │   ADMINER   │  │   MAILHOG   │                          │
│  │ Gestion BDD │  │ Emails dev  │                          │
│  └─────────────┘  └─────────────┘                          │
└─────────────────────────────────────────────────────────────┘
         │
    ┌─────────┐
    │ HOST OS │ ← Ports exposés : 8080, 8081, 8025, 3307, 6380
    └─────────┘
```

### Services et leurs rôles :

#### 🖥️ **APP (Application principale)**
- **Image** : PHP 8.3-FPM-Alpine + Nginx + Supervisor
- **Rôle** : Exécute l'application Symfony
- **Port** : 8080 → 80 (HTTP)
- **Contient** : Code source, dépendances Composer, cache Symfony

#### 🗄️ **DATABASE (Base de données)**
- **Image** : MySQL 8.0
- **Rôle** : Stockage des données (tâches, projets, utilisateurs)
- **Port** : 3307 → 3306 (MySQL)
- **Données** : Volume persistant pour la base

#### 🚀 **REDIS (Cache et sessions)**
- **Image** : Redis 7-Alpine
- **Rôle** : Cache applicatif et stockage des sessions
- **Port** : 6380 → 6379 (Redis)
- **Configuration** : Persistance activée avec mot de passe

#### 🔧 **ADMINER (Interface de gestion)**
- **Image** : Adminer latest
- **Rôle** : Interface web pour gérer la base de données
- **Port** : 8081 → 8080 (HTTP)
- **Accès** : Interface graphique pour MySQL

#### 📧 **MAILHOG (Capture d'emails)**
- **Image** : MailHog latest
- **Rôle** : Capture les emails envoyés en développement
- **Ports** : 8025 (Web), 1025 (SMTP)
- **Utilité** : Test des fonctionnalités email

---

## 📁 Fichiers importants

### Structure des fichiers Docker :
```
EcoTask/
├── 🐳 Dockerfile                    # Image multi-stage principale
├── 🐳 Dockerfile.test              # Image simplifiée pour tests
├── 🐳 docker-compose.yml           # Orchestration développement
├── 🐳 docker-compose.test.yml      # Configuration de test
├── 🐳 docker-compose.prod.yml      # Configuration production
├── 🐳 .dockerignore                # Exclusions pour le build
├── 🐳 .env.docker                  # Variables d'environnement
├── 📁 docker/
│   ├── nginx/
│   │   ├── nginx.conf              # Configuration Nginx principale
│   │   ├── default.conf            # Virtual host Symfony
│   │   └── test.conf               # Configuration test
│   ├── php/
│   │   └── php.ini                 # Configuration PHP personnalisée
│   ├── mysql/
│   │   └── my.cnf                  # Configuration MySQL optimisée
│   └── supervisor/
│       └── supervisord.conf        # Gestion des processus
├── 🛠️ Makefile                     # Commandes simplifiées
└── 📜 scripts/
    └── deploy.sh                   # Script de déploiement
```

---

## ⚙️ Fonctionnement détaillé

### 1. 🏗️ **Construction de l'image (Dockerfile.test)**

#### Étapes de construction :
```dockerfile
# 1. Image de base
FROM php:8.3-fpm-alpine

# 2. Installation des dépendances système
RUN apk add --no-cache git unzip curl libpng-dev mysql-dev nginx supervisor

# 3. Installation des extensions PHP
RUN docker-php-ext-install gd zip intl mbstring pdo pdo_mysql opcache

# 4. Installation de Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# 5. Configuration PHP optimisée
RUN echo "memory_limit=512M" >> /usr/local/etc/php/conf.d/custom.ini

# 6. Configuration Nginx et Supervisor
COPY docker/nginx/test.conf /etc/nginx/http.d/default.conf

# 7. Installation des dépendances Symfony
WORKDIR /var/www/html
COPY . .
RUN composer install --optimize-autoloader

# 8. Permissions et démarrage
RUN chown -R www-data:www-data /var/www/html
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
```

### 2. 🎼 **Orchestration (docker-compose.test.yml)**

#### Définition des services :
```yaml
services:
  app:                              # Service application
    build:
      context: .                    # Contexte de build = répertoire courant
      dockerfile: Dockerfile.test   # Fichier Docker à utiliser
    ports:
      - "8080:80"                   # Port host:container
    environment:                    # Variables d'environnement
      - APP_ENV=dev
      - DATABASE_URL=mysql://ecotask:ecotask_password@db:3306/ecotask_db
    depends_on:                     # Dépendances de démarrage
      - db
    networks:                       # Réseau Docker
      - ecotask_network

  db:                               # Service base de données
    image: mysql:8.0                # Image officielle MySQL
    environment:
      MYSQL_DATABASE: ecotask_db
      MYSQL_USER: ecotask
      MYSQL_PASSWORD: ecotask_password
    ports:
      - "3307:3306"                 # Accès externe à MySQL
    networks:
      - ecotask_network

networks:
  ecotask_network:                  # Réseau privé pour les services
    driver: bridge
```

### 3. 🔄 **Processus de démarrage**

#### Séquence de démarrage :
1. **Création du réseau** : `ecotask_network` (bridge)
2. **Démarrage de la base** : MySQL 8.0 avec configuration
3. **Construction de l'app** : Si l'image n'existe pas
4. **Démarrage de l'app** : Avec dépendance sur la base
5. **Vérification de santé** : Connexions et services

#### Processus internes du conteneur APP :
```bash
# Supervisor démarre et gère :
├── PHP-FPM (port 9000)           # Traitement des requêtes PHP
├── Nginx (port 80)               # Serveur web et reverse proxy
└── Logs centralisés              # Supervision des erreurs
```

### 4. 🌐 **Communication inter-services**

#### Résolution DNS interne :
- `app` → `db:3306` (connexion base de données)
- `app` → `redis:6379` (cache et sessions)
- `adminer` → `db:3306` (interface de gestion)

#### Variables d'environnement :
```bash
DATABASE_URL=mysql://ecotask:ecotask_password@db:3306/ecotask_db
#                                              ↑
#                                         Nom du service = hostname
```

---

## 🚀 Commandes essentielles

### Commandes de base :
```bash
# 📦 Construction et démarrage
sudo docker-compose -f docker-compose.test.yml up -d

# 📊 Vérification de l'état
sudo docker-compose -f docker-compose.test.yml ps

# 📋 Affichage des logs
sudo docker-compose -f docker-compose.test.yml logs -f

# 🛑 Arrêt des services
sudo docker-compose -f docker-compose.test.yml down

# 🧹 Nettoyage complet
sudo docker-compose -f docker-compose.test.yml down -v --rmi all
```

### Commandes d'administration :
```bash
# 🔧 Accès au shell de l'application
sudo docker exec -it ecotask_app_test sh

# 🗄️ Commandes Symfony dans le conteneur
sudo docker exec ecotask_app_test php bin/console doctrine:database:create
sudo docker exec ecotask_app_test php bin/console doctrine:migrations:migrate --no-interaction
sudo docker exec ecotask_app_test php bin/console doctrine:fixtures:load --no-interaction

# 📊 Vérification de la base de données
sudo docker exec ecotask_db_test mysql -u ecotask -pecotask_password -e "SHOW DATABASES;"

# 🚀 Test de connectivité
curl http://localhost:8080/health
```

### Commandes de développement :
```bash
# 🔄 Reconstruction forcée
sudo docker-compose -f docker-compose.test.yml build --no-cache

# 📝 Suivi des logs en temps réel
sudo docker-compose -f docker-compose.test.yml logs -f app

# 🔍 Inspection d'un conteneur
sudo docker inspect ecotask_app_test

# 📊 Statistiques des ressources
sudo docker stats ecotask_app_test ecotask_db_test
```

---

## 🔄 Workflows de développement

### Workflow quotidien :
```bash
# 1. 🌅 Démarrage de la journée
cd /path/to/EcoTask
sudo docker-compose -f docker-compose.test.yml up -d

# 2. 🔍 Vérification que tout fonctionne
curl http://localhost:8080/health

# 3. 💻 Développement
# Les fichiers sont synchronisés via volumes
# Les changements PHP sont pris en compte automatiquement

# 4. 🧪 Tests
sudo docker exec ecotask_app_test php bin/phpunit

# 5. 🌙 Fin de journée
sudo docker-compose -f docker-compose.test.yml down
```

### Workflow de mise à jour :
```bash
# 1. 📥 Récupération des changements
git pull origin main

# 2. 🔄 Reconstruction si nécessaire
sudo docker-compose -f docker-compose.test.yml build --no-cache

# 3. 🗄️ Mise à jour de la base
sudo docker exec ecotask_app_test php bin/console doctrine:migrations:migrate --no-interaction

# 4. ✅ Vérification
curl http://localhost:8080/health
```

### Workflow de débogage :
```bash
# 1. 📋 Vérification des logs
sudo docker-compose -f docker-compose.test.yml logs app

# 2. 🔧 Accès au conteneur
sudo docker exec -it ecotask_app_test sh

# 3. 🗄️ Vérification de la base
sudo docker exec ecotask_db_test mysql -u ecotask -pecotask_password ecotask_db

# 4. 🌐 Test des connexions
sudo docker exec ecotask_app_test ping db
sudo docker exec ecotask_app_test nc -zv db 3306
```

---

## 🔧 Dépannage

### Problèmes courants et solutions :

#### 🚫 **Erreur : Port déjà utilisé**
```bash
# Problème : Port 8080 déjà occupé
Error: bind: address already in use

# Solution : Identifier et arrêter le processus
sudo lsof -i :8080
sudo kill -9 <PID>
# Ou changer le port dans docker-compose.test.yml
```

#### 🗄️ **Erreur : Connexion base de données**
```bash
# Problème : SQLSTATE[HY000] [2002] Connection refused
# Solution : Vérifier que la base est démarrée
sudo docker-compose -f docker-compose.test.yml ps
sudo docker-compose -f docker-compose.test.yml logs db
```

#### 🐳 **Erreur : Image non trouvée**
```bash
# Problème : Image not found
# Solution : Construire l'image
sudo docker-compose -f docker-compose.test.yml build app
```

#### 💾 **Erreur : Permissions**
```bash
# Problème : Permission denied
# Solution : Corriger les permissions
sudo chown -R $USER:$USER .
sudo docker exec ecotask_app_test chown -R www-data:www-data /var/www/html
```

### Commandes de diagnostic :
```bash
# 🔍 État des conteneurs
sudo docker ps -a

# 📊 Utilisation des ressources
sudo docker stats

# 🌐 Inspection du réseau
sudo docker network ls
sudo docker network inspect ecotask_ecotask_network

# 💾 Gestion des volumes
sudo docker volume ls
sudo docker volume inspect ecotask_db_data
```

### Nettoyage et maintenance :
```bash
# 🧹 Nettoyage des images inutilisées
sudo docker image prune -f

# 🗑️ Nettoyage des volumes orphelins
sudo docker volume prune -f

# 🔄 Nettoyage complet du système
sudo docker system prune -af --volumes

# 📦 Reconstruction complète
sudo docker-compose -f docker-compose.test.yml down -v
sudo docker-compose -f docker-compose.test.yml build --no-cache
sudo docker-compose -f docker-compose.test.yml up -d
```

---

## 🎯 Points clés à retenir

### ✅ **Avantages de notre conteneurisation :**
1. **Isolation** : Chaque service dans son conteneur
2. **Reproductibilité** : Même environnement partout
3. **Scalabilité** : Ajout facile de nouvelles instances
4. **Maintenance** : Mise à jour indépendante des services
5. **Développement** : Environnement uniforme pour l'équipe

### 🎯 **Commandes à mémoriser :**
```bash
# Démarrage rapide
sudo docker-compose -f docker-compose.test.yml up -d

# Vérification santé
curl http://localhost:8080/health

# Logs en temps réel
sudo docker-compose -f docker-compose.test.yml logs -f

# Accès au conteneur
sudo docker exec -it ecotask_app_test sh

# Arrêt propre
sudo docker-compose -f docker-compose.test.yml down
```

### 🌐 **URLs importantes :**
- **Application** : http://localhost:8080
- **API Santé** : http://localhost:8080/health
- **Adminer** : http://localhost:8081
- **MailHog** : http://localhost:8025

---

---

## 📚 Annexes

### 🔗 **Fichiers de configuration clés :**

#### `.env` (Variables d'environnement)
```bash
APP_ENV=dev
APP_DEBUG=1
DATABASE_URL=mysql://ecotask:ecotask_password@db:3306/ecotask_db
MESSENGER_TRANSPORT_DSN=doctrine://default
```

#### `docker/nginx/test.conf` (Configuration Nginx)
```nginx
server {
    listen 80;
    root /var/www/html/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location ~ \.php$ {
        fastcgi_pass app:9000;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
}
```

#### `docker/supervisor/supervisord.conf` (Gestion des processus)
```ini
[supervisord]
nodaemon=true

[program:php-fpm]
command=php-fpm --nodaemonize
autostart=true
autorestart=true

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
```

### 🚀 **Makefile simplifié :**
```makefile
# Commandes rapides
up:
	sudo docker-compose -f docker-compose.test.yml up -d

down:
	sudo docker-compose -f docker-compose.test.yml down

logs:
	sudo docker-compose -f docker-compose.test.yml logs -f

shell:
	sudo docker exec -it ecotask_app_test sh

health:
	curl http://localhost:8080/health

db-reset:
	sudo docker exec ecotask_app_test php bin/console doctrine:database:drop --force --if-exists
	sudo docker exec ecotask_app_test php bin/console doctrine:database:create
	sudo docker exec ecotask_app_test php bin/console doctrine:migrations:migrate --no-interaction
	sudo docker exec ecotask_app_test php bin/console doctrine:fixtures:load --no-interaction
```

### 📋 **Checklist de démarrage :**
- [ ] Docker et Docker Compose installés
- [ ] Ports 8080, 8081, 3307 libres
- [ ] Fichier `.env` configuré
- [ ] Commande `sudo docker-compose -f docker-compose.test.yml up -d` exécutée
- [ ] Test `curl http://localhost:8080/health` réussi
- [ ] Base de données migrée et peuplée

---

*Ce guide couvre l'essentiel de la conteneurisation EcoTask. Pour des questions spécifiques, consultez la documentation Docker officielle ou les logs des conteneurs.*
