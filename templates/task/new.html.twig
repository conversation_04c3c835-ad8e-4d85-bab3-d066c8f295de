{% extends 'base.html.twig' %}

{% block title %}Nouvelle tâche - EcoTask{% endblock %}

{% block body %}
<!-- Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8">
    <div class="mb-4 lg:mb-0">
        <div class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
            <a href="{{ path('app_task_index') }}" class="hover:text-eco-green-600 transition-colors duration-200">Tâches</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <span>Nouvelle tâche</span>
        </div>
        <h1 class="text-3xl font-bold text-gray-900">C<PERSON>er une nouvelle tâche</h1>
        <p class="text-gray-600 mt-1">Ajoutez une tâche à votre projet et calculez son impact environnemental</p>
    </div>
    <div class="flex space-x-3">
        <a href="{{ path('app_task_index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-all duration-200">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Retour à la liste
        </a>
    </div>
</div>

<!-- Form Card -->
<div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
    <div class="bg-gradient-to-r from-eco-green-50 to-eco-blue-50 px-6 py-4 border-b border-gray-100">
        <h2 class="text-xl font-semibold text-gray-900 flex items-center">
            <svg class="w-6 h-6 mr-2 text-eco-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Informations de la tâche
        </h2>
    </div>
    <div class="p-6">
        {{ form_start(form, {'attr': {'class': 'space-y-6'}}) }}

        <!-- Title and Priority -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div class="lg:col-span-3">
                <label for="{{ form.title.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.title.vars.label }}
                </label>
                {{ form_widget(form.title, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200'}}) }}
                {{ form_errors(form.title) }}
            </div>
            <div>
                <label for="{{ form.priority.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.priority.vars.label }}
                </label>
                {{ form_widget(form.priority, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200'}}) }}
                {{ form_errors(form.priority) }}
            </div>
        </div>

        <!-- Description -->
        <div>
            <label for="{{ form.description.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                {{ form.description.vars.label }}
            </label>
            {{ form_widget(form.description, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200', 'rows': 4}}) }}
            {{ form_errors(form.description) }}
        </div>

        <!-- Project and Assignment -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="{{ form.project.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.project.vars.label }}
                    <span class="text-red-500">*</span>
                </label>
                {{ form_widget(form.project, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200'}}) }}
                {{ form_errors(form.project) }}
            </div>
            <div>
                <label for="{{ form.assignedTo.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.assignedTo.vars.label }}
                </label>
                {{ form_widget(form.assignedTo, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200'}}) }}
                {{ form_errors(form.assignedTo) }}
            </div>
        </div>

        <!-- Type, Status, and Due Date -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
                <label for="{{ form.type.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.type.vars.label }}
                    <span class="text-red-500">*</span>
                </label>
                {{ form_widget(form.type, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200'}}) }}
                {{ form_errors(form.type) }}
                <p class="mt-1 text-xs text-gray-500">{{ form.type.vars.help }}</p>
            </div>
            <div>
                <label for="{{ form.status.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.status.vars.label }}
                </label>
                {{ form_widget(form.status, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200'}}) }}
                {{ form_errors(form.status) }}
            </div>
            <div>
                <label for="{{ form.dueDate.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.dueDate.vars.label }}
                </label>
                {{ form_widget(form.dueDate, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200'}}) }}
                {{ form_errors(form.dueDate) }}
            </div>
        </div>

        <!-- Hours -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="{{ form.estimatedHours.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.estimatedHours.vars.label }}
                </label>
                {{ form_widget(form.estimatedHours, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200', 'step': '0.5', 'min': '0'}}) }}
                {{ form_errors(form.estimatedHours) }}
                <p class="mt-1 text-xs text-gray-500">{{ form.estimatedHours.vars.help }}</p>
            </div>
            <div>
                <label for="{{ form.actualHours.vars.id }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.actualHours.vars.label }}
                </label>
                {{ form_widget(form.actualHours, {'attr': {'class': 'block w-full rounded-lg border-gray-300 shadow-sm focus:border-eco-green-500 focus:ring-eco-green-500 transition-colors duration-200', 'step': '0.5', 'min': '0'}}) }}
                {{ form_errors(form.actualHours) }}
                <p class="mt-1 text-xs text-gray-500">{{ form.actualHours.vars.help }}</p>
            </div>
        </div>

        <!-- CO2 Impact Calculator -->
        <div class="bg-gradient-to-br from-eco-green-50 to-emerald-100 rounded-xl p-6 border border-eco-green-200">
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-eco-green-500 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-eco-green-900 mb-2">Impact environnemental</h3>
                    <p class="text-eco-green-700 mb-3">Cette tâche générera approximativement :</p>
                    <div id="co2-calculation" class="text-2xl font-bold text-eco-green-800 mb-3">
                        0.00 kg CO₂
                    </div>
                    <p class="text-sm text-eco-green-600">
                        Le calcul est basé sur le type de tâche et les heures estimées/réelles.
                        <br>
                        <span class="font-medium">Bureautique légère :</span> 0.1 kg CO₂/h •
                        <span class="font-medium">Technique :</span> 1.0 kg CO₂/h •
                        <span class="font-medium">Intensive :</span> 3.5 kg CO₂/h
                    </p>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row justify-between space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-200">
            <a href="{{ path('app_task_index') }}" class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-all duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Annuler
            </a>
            <button type="submit" class="inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-eco-green-600 to-eco-green-700 text-white font-semibold rounded-lg hover:from-eco-green-700 hover:to-eco-green-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Créer la tâche
            </button>
        </div>

        {{ form_end(form) }}
    </div>
</div>

<script>
// Calcul CO2 en temps réel
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.querySelector('#task_type');
    const estimatedHoursInput = document.querySelector('#task_estimatedHours');
    const actualHoursInput = document.querySelector('#task_actualHours');
    const co2Display = document.querySelector('#co2-calculation');

    const co2Rates = {
        'office_light': 0.1,
        'technical': 1.0,
        'energy_intensive': 3.5
    };

    function updateCo2Calculation() {
        if (!typeSelect || !estimatedHoursInput || !actualHoursInput || !co2Display) return;

        const type = typeSelect.value;
        const estimatedHours = parseFloat(estimatedHoursInput.value) || 0;
        const actualHours = parseFloat(actualHoursInput.value) || 0;
        const hours = actualHours || estimatedHours;
        const rate = co2Rates[type] || 0.1;
        const co2 = hours * rate;

        // Animation du changement
        co2Display.style.transform = 'scale(1.1)';
        co2Display.style.transition = 'transform 0.2s ease-out';

        setTimeout(() => {
            co2Display.textContent = co2.toFixed(2) + ' kg CO₂';
            co2Display.style.transform = 'scale(1)';
        }, 100);

        // Changement de couleur selon l'impact
        co2Display.className = 'text-2xl font-bold transition-colors duration-300 ' +
            (co2 > 10 ? 'text-red-600' : co2 > 5 ? 'text-orange-600' : 'text-eco-green-800');
    }

    if (typeSelect) typeSelect.addEventListener('change', updateCo2Calculation);
    if (estimatedHoursInput) estimatedHoursInput.addEventListener('input', updateCo2Calculation);
    if (actualHoursInput) actualHoursInput.addEventListener('input', updateCo2Calculation);

    // Calcul initial
    setTimeout(updateCo2Calculation, 100);
});
</script>
{% endblock %}
