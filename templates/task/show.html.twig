{% extends 'base.html.twig' %}

{% block title %}{{ task.title }} - EcoTask{% endblock %}

{% block body %}
<!-- Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8">
    <div class="mb-4 lg:mb-0">
        <div class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
            <a href="{{ path('app_task_index') }}" class="hover:text-eco-green-600 transition-colors duration-200">Tâches</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <span>{{ task.title }}</span>
        </div>
        <h1 class="text-3xl font-bold text-gray-900">D<PERSON><PERSON> de la tâche</h1>
        <p class="text-gray-600 mt-1">Consul<PERSON>z et gérez les informations de cette tâche</p>
    </div>
    <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
        <a href="{{ path('app_task_edit', {'id': task.id}) }}" class="inline-flex items-center px-4 py-2 bg-eco-blue-600 text-white font-medium rounded-lg hover:bg-eco-blue-700 transition-all duration-200 shadow-md hover:shadow-lg">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            Modifier
        </a>
        <a href="{{ path('app_task_index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-all duration-200">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Retour à la liste
        </a>
    </div>
</div>

<!-- Main Task Card -->
<div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden mb-8">
    <!-- Header -->
    <div class="bg-gradient-to-r from-eco-green-50 to-eco-blue-50 px-6 py-4 border-b border-gray-100">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <h2 class="text-2xl font-bold text-gray-900 mb-2 sm:mb-0">{{ task.title }}</h2>
            <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-semibold bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-md">
                {{ task.co2Emission|number_format(2) }} kg CO₂
            </span>
        </div>
    </div>

    <!-- Content -->
    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-6">
            <!-- Informations générales -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-eco-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Informations générales
                </h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600 font-medium">Projet :</span>
                        <a href="{{ path('app_project_show', {'id': task.project.id}) }}" class="text-eco-blue-600 hover:text-eco-blue-700 font-medium transition-colors duration-200">
                            {{ task.project.name }}
                        </a>
                    </div>
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600 font-medium">Assigné à :</span>
                        <div class="flex items-center">
                            {% if task.assignedTo %}
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-eco-green-100 rounded-full flex items-center justify-center">
                                        <svg class="w-3 h-3 text-eco-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                    </div>
                                    <span class="text-gray-900 font-medium">{{ task.assignedTo.fullName }}</span>
                                </div>
                            {% else %}
                                <span class="text-gray-400 italic">Non assigné</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600 font-medium">Priorité :</span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ task.priority == 'urgent' ? 'bg-red-100 text-red-800' : (task.priority == 'high' ? 'bg-orange-100 text-orange-800' : (task.priority == 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800')) }}">
                            {{ task.priority == 'urgent' ? 'Urgent' : (task.priority == 'high' ? 'Haute' : (task.priority == 'medium' ? 'Moyenne' : 'Faible')) }}
                        </span>
                    </div>
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600 font-medium">Statut :</span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ task.status == 'done' ? 'bg-green-100 text-green-800' : (task.status == 'in_progress' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800') }}">
                            {{ task.status == 'done' ? 'Terminée' : (task.status == 'in_progress' ? 'En cours' : 'À faire') }}
                        </span>
                    </div>
                    <div class="flex items-center justify-between py-2">
                        <span class="text-gray-600 font-medium">Type :</span>
                        <div class="flex items-center space-x-2">
                            {% if task.type == 'energy_intensive' %}
                                <span class="text-2xl">⚡</span>
                                <span class="text-gray-900 font-medium">Forte intensité</span>
                            {% elseif task.type == 'technical' %}
                                <span class="text-2xl">⚙️</span>
                                <span class="text-gray-900 font-medium">Technique</span>
                            {% else %}
                                <span class="text-2xl">📄</span>
                                <span class="text-gray-900 font-medium">Bureautique</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            <!-- Dates et temps -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-eco-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Dates et temps
                </h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600 font-medium">Créée le :</span>
                        <span class="text-gray-900">{{ task.createdAt|date('d/m/Y à H:i') }}</span>
                    </div>
                    {% if task.updatedAt %}
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600 font-medium">Modifiée le :</span>
                        <span class="text-gray-900">{{ task.updatedAt|date('d/m/Y à H:i') }}</span>
                    </div>
                    {% endif %}
                    {% if task.dueDate %}
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600 font-medium">Échéance :</span>
                        <div class="flex items-center space-x-2">
                            <span class="{{ task.isOverdue ? 'text-red-600' : 'text-gray-900' }}">
                                {{ task.dueDate|date('d/m/Y à H:i') }}
                            </span>
                            {% if task.isOverdue %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    En retard
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    {% if task.completedAt %}
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600 font-medium">Terminée le :</span>
                        <span class="text-green-600 font-medium">{{ task.completedAt|date('d/m/Y à H:i') }}</span>
                    </div>
                    {% endif %}
                    {% if task.estimatedHours %}
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600 font-medium">Heures estimées :</span>
                        <span class="text-gray-900">{{ task.estimatedHours }}h</span>
                    </div>
                    {% endif %}
                    {% if task.actualHours %}
                    <div class="flex items-center justify-between py-2">
                        <span class="text-gray-600 font-medium">Heures réelles :</span>
                        <span class="text-gray-900 font-semibold">{{ task.actualHours }}h</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Description -->
        {% if task.description %}
        <div class="mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Description
            </h3>
            <div class="bg-gray-50 rounded-lg p-6 border border-gray-200">
                <p class="text-gray-700 leading-relaxed">{{ task.description|nl2br }}</p>
            </div>
        </div>
        {% endif %}

        <!-- Impact environnemental -->
        <div class="mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Impact environnemental
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-gradient-to-br from-green-50 to-emerald-100 rounded-xl p-6 text-center border border-green-200">
                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h4 class="text-2xl font-bold text-green-700 mb-1">{{ task.co2Emission|number_format(2) }}</h4>
                    <p class="text-green-600 font-medium">kg CO₂ émis</p>
                </div>
                <div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 text-center border border-blue-200">
                    <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h4 class="text-2xl font-bold text-blue-700 mb-1">{{ task.co2Rate|number_format(1) }}</h4>
                    <p class="text-blue-600 font-medium">kg CO₂/heure</p>
                </div>
                <div class="bg-gradient-to-br from-orange-50 to-yellow-100 rounded-xl p-6 text-center border border-orange-200">
                    <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h4 class="text-2xl font-bold text-orange-700 mb-1">{{ (task.actualHours ?? task.estimatedHours ?? 0)|number_format(1) }}</h4>
                    <p class="text-orange-600 font-medium">heures de travail</p>
                </div>
            </div>
            <div class="bg-eco-green-50 rounded-lg p-4 border border-eco-green-200">
                <div class="flex items-start space-x-3">
                    <svg class="w-5 h-5 text-eco-green-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <p class="text-sm text-eco-green-700 font-medium mb-1">Calcul de l'empreinte carbone</p>
                        <p class="text-sm text-eco-green-600">
                            Le calcul est basé sur le type de tâche et le temps de travail.
                            {% if task.type == 'office_light' %}
                                Les tâches de bureautique légère ont un faible impact énergétique (0.1 kg CO₂/h).
                            {% elseif task.type == 'technical' %}
                                Les tâches techniques incluent l'utilisation de serveurs et outils de développement (1.0 kg CO₂/h).
                            {% else %}
                                Les tâches à forte intensité énergétique nécessitent des ressources importantes (3.5 kg CO₂/h).
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions -->
<div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
    <div class="flex flex-col sm:flex-row justify-between space-y-3 sm:space-y-0 sm:space-x-4">
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            <form method="post" action="{{ path('app_task_toggle_status', {'id': task.id}) }}" class="inline">
                <input type="hidden" name="_token" value="{{ csrf_token('toggle' ~ task.id) }}">
                <button type="submit" class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-eco-green-600 to-eco-green-700 text-white font-semibold rounded-lg hover:from-eco-green-700 hover:to-eco-green-800 transition-all duration-200 shadow-md hover:shadow-lg">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    {% if task.status == 'todo' %}
                        Commencer la tâche
                    {% elseif task.status == 'in_progress' %}
                        Marquer comme terminée
                    {% else %}
                        Rouvrir la tâche
                    {% endif %}
                </button>
            </form>
        </div>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            <a href="{{ path('app_task_edit', {'id': task.id}) }}" class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 bg-eco-blue-600 text-white font-semibold rounded-lg hover:bg-eco-blue-700 transition-all duration-200 shadow-md hover:shadow-lg">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Modifier
            </a>
            <form method="post" action="{{ path('app_task_delete', {'id': task.id}) }}" class="inline" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette tâche ?')">
                <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ task.id) }}">
                <button type="submit" class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 bg-red-600 text-white font-semibold rounded-lg hover:bg-red-700 transition-all duration-200 shadow-md hover:shadow-lg">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Supprimer
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}
