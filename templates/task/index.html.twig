{% extends 'base.html.twig' %}

{% block title %}Gestion des tâches - EcoTask{% endblock %}

{% block body %}
<!-- Header -->
<div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
    <div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Gestion des tâches</h1>
        <p class="text-gray-600">Organisez votre travail tout en préservant l'environnement</p>
    </div>
    <div class="mt-4 sm:mt-0">
        <a href="{{ path('app_task_new') }}" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-eco-green-600 to-eco-green-700 text-white font-semibold rounded-lg hover:from-eco-green-700 hover:to-eco-green-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Nouvelle tâche
        </a>
    </div>
</div>

{% if tasks|length > 0 %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for task in tasks %}
            <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-eco-green-200 group {{ task.priority == 'urgent' ? 'border-l-4 border-l-red-500' : (task.priority == 'high' ? 'border-l-4 border-l-orange-500' : (task.priority == 'medium' ? 'border-l-4 border-l-yellow-500' : 'border-l-4 border-l-green-500')) }}">
                <!-- Header -->
                <div class="p-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-xl">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-600">{{ task.project.name }}</span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-gradient-to-r from-emerald-500 to-teal-600 text-white">
                            {{ task.co2Emission|number_format(2) }} kg CO₂
                        </span>
                    </div>
                </div>

                <!-- Body -->
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2 group-hover:text-eco-green-700 transition-colors duration-200">
                        {{ task.title }}
                    </h3>
                    <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                        {{ task.description|slice(0, 120) }}{% if task.description|length > 120 %}...{% endif %}
                    </p>

                    <!-- Status badges -->
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ task.priority == 'urgent' ? 'bg-red-100 text-red-800' : (task.priority == 'high' ? 'bg-orange-100 text-orange-800' : (task.priority == 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800')) }}">
                            {{ task.priority == 'urgent' ? 'Urgent' : (task.priority == 'high' ? 'Haute' : (task.priority == 'medium' ? 'Moyenne' : 'Faible')) }}
                        </span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ task.status == 'done' ? 'bg-green-100 text-green-800' : (task.status == 'in_progress' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800') }}">
                            {{ task.status == 'done' ? 'Terminée' : (task.status == 'in_progress' ? 'En cours' : 'À faire') }}
                        </span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            {% if task.type == 'energy_intensive' %}
                                ⚡ Intensive
                            {% elseif task.type == 'technical' %}
                                ⚙️ Technique
                            {% else %}
                                📄 Bureautique
                            {% endif %}
                        </span>
                    </div>

                    <!-- Task details -->
                    <div class="space-y-3 mb-4">
                        {% if task.assignedTo %}
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                {{ task.assignedTo.fullName }}
                            </div>
                        {% endif %}

                        {% if task.dueDate %}
                            <div class="flex items-center text-sm {{ task.isOverdue ? 'text-red-600' : 'text-gray-600' }}">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                Échéance : {{ task.dueDate|date('d/m/Y') }}
                                {% if task.isOverdue %}
                                    <svg class="w-4 h-4 ml-1 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                {% endif %}
                            </div>
                        {% endif %}

                        {% if task.estimatedHours or task.actualHours %}
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {% if task.actualHours %}
                                    {{ task.actualHours }}h réelles
                                    {% if task.estimatedHours %} ({{ task.estimatedHours }}h estimées){% endif %}
                                {% else %}
                                    {{ task.estimatedHours }}h estimées
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Actions -->
                <div class="px-6 py-4 bg-gray-50 rounded-b-xl border-t border-gray-100">
                    <div class="flex items-center justify-between">
                        <div class="flex space-x-2">
                            <a href="{{ path('app_task_show', {'id': task.id}) }}" class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                Voir
                            </a>
                            <a href="{{ path('app_task_edit', {'id': task.id}) }}" class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Modifier
                            </a>
                        </div>
                        <div class="flex space-x-2">
                            <form method="post" action="{{ path('app_task_toggle_status', {'id': task.id}) }}" class="inline">
                                <input type="hidden" name="_token" value="{{ csrf_token('toggle' ~ task.id) }}">
                                <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-eco-green-300 text-xs font-medium rounded-md text-eco-green-700 bg-eco-green-50 hover:bg-eco-green-100 transition-colors duration-200" title="Changer le statut">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Statut
                                </button>
                            </form>
                            <form method="post" action="{{ path('app_task_delete', {'id': task.id}) }}" class="inline" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette tâche ?')">
                                <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ task.id) }}">
                                <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-red-300 text-xs font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 transition-colors duration-200">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    Supprimer
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <!-- Empty state -->
    <div class="text-center py-16">
        <div class="max-w-md mx-auto">
            <svg class="w-24 h-24 text-gray-300 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
            </svg>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Aucune tâche trouvée</h3>
            <p class="text-gray-500 mb-8">Commencez par créer votre première tâche éco-responsable !</p>
            <a href="{{ path('app_task_new') }}" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-eco-green-600 to-eco-green-700 text-white font-semibold rounded-lg hover:from-eco-green-700 hover:to-eco-green-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Créer une tâche
            </a>
        </div>
    </div>
{% endif %}
{% endblock %}
