# 🚀 Guide de Déploiement Serveur EcoTask

Ce guide vous explique comment configurer un serveur pour déployer automatiquement EcoTask via GitHub Actions.

## 📋 Prérequis

### 1. Serveur Linux (Ubuntu 22.04 recommandé)
- VPS avec au moins 1GB RAM
- Accès SSH root ou sudo
- Nom de domaine (optionnel mais recommandé)

### 2. Logiciels requis sur le serveur
```bash
# Mise à jour du système
sudo apt update && sudo apt upgrade -y

# Installation des dépendances
sudo apt install -y nginx mysql-server php8.3-fpm php8.3-mysql php8.3-xml php8.3-mbstring php8.3-curl php8.3-zip php8.3-intl php8.3-gd composer git unzip

# Installation de Node.js (pour les assets)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
```

## 🔧 Configuration du Serveur

### 1. Configuration MySQL
```bash
# Sécuriser MySQL
sudo mysql_secure_installation

# Créer les bases de données
sudo mysql -u root -p
```

```sql
-- Dans MySQL
CREATE DATABASE ecotask_staging;
CREATE DATABASE ecotask_production;
CREATE USER 'ecotask'@'localhost' IDENTIFIED BY 'votre_mot_de_passe_fort';
GRANT ALL PRIVILEGES ON ecotask_staging.* TO 'ecotask'@'localhost';
GRANT ALL PRIVILEGES ON ecotask_production.* TO 'ecotask'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 2. Configuration des répertoires
```bash
# Créer les répertoires de déploiement
sudo mkdir -p /var/www/ecotask/staging
sudo mkdir -p /var/www/ecotask/production
sudo mkdir -p /var/www/ecotask/backups

# Cloner le repository
cd /var/www/ecotask/staging
sudo git clone https://github.com/sami53tk/EcoTask.git .
cd /var/www/ecotask/production
sudo git clone https://github.com/sami53tk/EcoTask.git .

# Ajuster les permissions
sudo chown -R www-data:www-data /var/www/ecotask
sudo chmod -R 755 /var/www/ecotask
```

### 3. Configuration Nginx

Créer `/etc/nginx/sites-available/ecotask-staging`:
```nginx
server {
    listen 80;
    server_name staging.votre-domaine.com;
    root /var/www/ecotask/staging/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location ~ ^/index\.php(/|$) {
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        internal;
    }

    location ~ \.php$ {
        return 404;
    }
}
```

Créer `/etc/nginx/sites-available/ecotask-production`:
```nginx
server {
    listen 80;
    server_name votre-domaine.com;
    root /var/www/ecotask/production/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location ~ ^/index\.php(/|$) {
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        internal;
    }

    location ~ \.php$ {
        return 404;
    }
}
```

Activer les sites :
```bash
sudo ln -s /etc/nginx/sites-available/ecotask-staging /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/ecotask-production /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 4. Configuration des fichiers .env

Créer `/var/www/ecotask/staging/.env`:
```bash
APP_ENV=prod
APP_SECRET=votre_secret_staging_unique
DATABASE_URL=mysql://ecotask:votre_mot_de_passe@localhost:3306/ecotask_staging
MAILER_DSN=smtp://localhost:1025
```

Créer `/var/www/ecotask/production/.env`:
```bash
APP_ENV=prod
APP_SECRET=votre_secret_production_unique
DATABASE_URL=mysql://ecotask:votre_mot_de_passe@localhost:3306/ecotask_production
MAILER_DSN=smtp://localhost:587
```

## 🔑 Configuration GitHub Secrets

Dans votre repository GitHub, allez dans Settings > Secrets and variables > Actions et ajoutez :

### Secrets requis :
- `SERVER_HOST` : IP de votre serveur (ex: `*************`)
- `SERVER_USER` : utilisateur SSH (ex: `ubuntu` ou `root`)
- `SERVER_SSH_KEY` : clé privée SSH (contenu du fichier `~/.ssh/id_rsa`)
- `SERVER_PATH` : chemin de base (ex: `/var/www/ecotask`)
- `SERVER_PORT` : port SSH (ex: `22`)
- `STAGING_URL` : URL de staging (ex: `http://staging.votre-domaine.com`)
- `PRODUCTION_URL` : URL de production (ex: `http://votre-domaine.com`)

### Secrets Telegram (déjà configurés) :
- `TELEGRAM_BOT_TOKEN` : `**********************************************`
- `TELEGRAM_CHAT_ID` : `8138991176`

## 🔐 Configuration SSH

### 1. Générer une clé SSH (sur votre machine locale)
```bash
ssh-keygen -t rsa -b 4096 -C "github-actions@ecotask"
```

### 2. Copier la clé publique sur le serveur
```bash
ssh-copy-id -i ~/.ssh/id_rsa.pub user@votre-serveur
```

### 3. Tester la connexion
```bash
ssh -i ~/.ssh/id_rsa user@votre-serveur
```

## 🚀 Déploiement Initial

### 1. Premier déploiement staging
```bash
cd /var/www/ecotask/staging
sudo -u www-data composer install --no-dev --optimize-autoloader
sudo -u www-data php bin/console doctrine:migrations:migrate --no-interaction --env=prod
sudo -u www-data php bin/console cache:clear --env=prod
```

### 2. Premier déploiement production
```bash
cd /var/www/ecotask/production
sudo -u www-data composer install --no-dev --optimize-autoloader
sudo -u www-data php bin/console doctrine:migrations:migrate --no-interaction --env=prod
sudo -u www-data php bin/console cache:clear --env=prod
```

## 📊 Test du Monitoring

Une fois configuré, testez :

1. **Health Check Staging** : `http://staging.votre-domaine.com/health`
2. **Health Check Production** : `http://votre-domaine.com/health`

## 🔄 Utilisation

### Déploiement automatique :
- **Staging** : Push sur `main` → déploiement automatique
- **Production** : Créer un tag `v1.0.0` → déploiement automatique

### Déploiement manuel :
- Aller dans Actions > Deploy to Server > Run workflow
- Choisir l'environnement (staging/production)

## 🚨 Dépannage

### Problèmes courants :
1. **Permissions** : `sudo chown -R www-data:www-data /var/www/ecotask`
2. **Cache** : `sudo -u www-data php bin/console cache:clear --env=prod`
3. **Nginx** : `sudo nginx -t && sudo systemctl reload nginx`
4. **PHP-FPM** : `sudo systemctl status php8.3-fpm`

### Logs utiles :
- Nginx : `/var/log/nginx/error.log`
- PHP-FPM : `/var/log/php8.3-fpm.log`
- Application : `/var/www/ecotask/*/var/log/prod.log`
