# 🚀 CI/CD Pipeline - EcoTask

## 📋 Vue d'ensemble

Ce projet utilise **GitHub Actions** pour automatiser les tests, la qualité du code et les déploiements.

## 🔄 Workflows Configurés

### 1. **CI - Tests et Qualité** (`.github/workflows/ci.yml`)

**Déclencheurs :**
- Push sur `main` et `develop`
- Pull requests vers `main` et `develop`
- Déclenchement manuel

**Jobs :**

#### 🧪 Tests PHP
- **Services** : MySQL 8.0, Redis 7
- **PHP** : 8.3 avec extensions (mbstring, xml, pdo_mysql, redis)
- **Actions** :
  - Installation des dépendances Composer
  - Configuration base de données de test
  - Exécution PHPUnit avec couverture
  - Upload vers Codecov

#### 🔍 Qualité du Code
- **Vérifications** :
  - Syntaxe PHP (`php -l`)
  - Style de code (PHP CS Fixer)
  - Analyse statique (PHPStan niveau 8)
  - Audit de sécurité (Composer audit)

#### 🐳 Tests Docker
- **Actions** :
  - Build image de test
  - Démarrage environnement complet
  - Tests de santé automatiques
  - Validation des services

#### 🔒 Tests de Sécurité
- **Outils** :
  - Trivy (scan de vulnérabilités)
  - Upload des résultats vers GitHub Security

### 2. **Déploiement** (`.github/workflows/deploy.yml`)

**Déclencheurs :**
- Push sur `main` (staging)
- Tags `v*` (production)
- Déclenchement manuel

**Jobs :**

#### 🏗️ Build & Push Images
- **Registry** : GitHub Container Registry (ghcr.io)
- **Caching** : GitHub Actions cache
- **Multi-stage** : Image optimisée pour production

#### 🎭 Déploiement Staging
- **Environnement** : staging.ecotask.example.com
- **Vérifications** : Health checks automatiques
- **Notifications** : Slack (#deployments)

#### 🏭 Déploiement Production
- **Environnement** : ecotask.example.com
- **Sécurité** : Approbation manuelle requise
- **Monitoring** : Activation post-déploiement
- **Notifications** : Slack (#production)

#### 🔄 Rollback Automatique
- **Déclenchement** : Échec de déploiement
- **Action** : Retour version précédente
- **Alertes** : Notification immédiate

### 3. **Monitoring & Maintenance** (`.github/workflows/monitoring.yml`)

**Planification :**
- **Health checks** : Toutes les heures
- **Maintenance** : Quotidienne à 6h00 UTC

**Jobs :**

#### 🔍 Health Check
- **Fréquence** : Horaire
- **Vérifications** :
  - Disponibilité applications
  - Performance base de données
  - Espace disque
- **Rapports** : Slack automatique

#### 🔒 Scan de Sécurité
- **Fréquence** : Quotidienne
- **Outils** :
  - Composer audit
  - Trivy scan
- **Artefacts** : Rapports de sécurité

#### ⚡ Tests de Performance
- **Outils** : Artillery (simulation)
- **Métriques** :
  - Temps de réponse
  - Débit
  - Utilisation ressources

#### 💾 Sauvegarde
- **Fréquence** : Quotidienne
- **Contenu** :
  - Base de données
  - Fichiers uploads
- **Stockage** : Cloud (simulation)

#### 🧹 Nettoyage
- **Actions** :
  - Suppression anciens artefacts
  - Nettoyage images Docker
  - Optimisation espace

## 🛠️ Outils de Qualité Configurés

### PHP CS Fixer
```bash
# Vérification
make cs-check

# Correction automatique
make cs-fix
```

**Configuration** : `.php-cs-fixer.dist.php`
- Règles Symfony + PSR-12
- PHP 8.3 optimisé
- Doctrine annotations

### PHPStan
```bash
# Analyse statique
make phpstan
```

**Configuration** : `phpstan.neon`
- Niveau 8 (maximum)
- Extensions Symfony + Doctrine
- Types stricts activés

### PHPUnit
```bash
# Tests unitaires
make test

# Avec couverture
make test-coverage
```

**Configuration** : `phpunit.dist.xml`
- PHP 8.3 compatible
- Bootstrap personnalisé
- Couverture de code

## 🚀 Commandes Makefile

### Pipeline Complet
```bash
# Pipeline CI local
make ci-pipeline

# Préparation commit
make pre-commit

# Installation outils dev
make install-dev-tools
```

### Tests et Qualité
```bash
# Tests
make test

# Style de code
make cs-check
make cs-fix

# Analyse statique
make phpstan

# Sécurité
make security-check
```

### Docker
```bash
# Démarrage rapide
make quick-start

# Développement complet
make dev-start

# Nettoyage
make clean-docker
```

## 📊 Métriques et Monitoring

### Couverture de Code
- **Outil** : PHPUnit + Codecov
- **Objectif** : > 80%
- **Rapports** : Automatiques sur PR

### Qualité du Code
- **PHPStan** : Niveau 8/8
- **PHP CS Fixer** : 100% conforme
- **Sécurité** : 0 vulnérabilité

### Performance
- **Temps de réponse** : < 200ms
- **Disponibilité** : > 99.9%
- **Monitoring** : Temps réel

## 🔧 Configuration Locale

### Prérequis
```bash
# Installation des outils
make install-dev-tools

# Configuration Git hooks (optionnel)
cp scripts/pre-commit.sh .git/hooks/pre-commit
chmod +x .git/hooks/pre-commit
```

### Workflow de Développement
```bash
# 1. Développement
git checkout -b feature/nouvelle-fonctionnalite

# 2. Tests locaux
make pre-commit

# 3. Commit et push
git add .
git commit -m "feat: nouvelle fonctionnalité"
git push origin feature/nouvelle-fonctionnalite

# 4. Pull Request
# → CI automatique déclenché
```

## 🚨 Gestion des Erreurs

### CI Échoue
1. **Vérifier les logs** dans GitHub Actions
2. **Reproduire localement** : `make ci-pipeline`
3. **Corriger** et recommiter

### Déploiement Échoue
1. **Rollback automatique** activé
2. **Notification Slack** envoyée
3. **Investigation** via logs

### Monitoring Alerte
1. **Notification immédiate** Slack
2. **Escalade** si non résolu en 15min
3. **Post-mortem** obligatoire

## 📈 Évolutions Prévues

### Court Terme
- [ ] Tests d'intégration complets
- [ ] Déploiement multi-environnements
- [ ] Monitoring avancé (Grafana)

### Moyen Terme
- [ ] Tests de charge automatisés
- [ ] Déploiement blue-green
- [ ] Alertes intelligentes

### Long Terme
- [ ] GitOps avec ArgoCD
- [ ] Infrastructure as Code
- [ ] Chaos Engineering

## 🔗 Liens Utiles

- **GitHub Actions** : [Documentation](https://docs.github.com/en/actions)
- **PHP CS Fixer** : [Configuration](https://cs.symfony.com/)
- **PHPStan** : [Règles](https://phpstan.org/user-guide/rule-levels)
- **Codecov** : [Rapports](https://codecov.io/)

---

**Maintenu par** : Équipe EcoTask  
**Dernière mise à jour** : $(date +%Y-%m-%d)
