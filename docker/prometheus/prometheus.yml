# Configuration Prometheus pour EcoTask
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus lui-même
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Application EcoTask
  - job_name: 'ecotask-app'
    static_configs:
      - targets: ['app:80']
    metrics_path: '/health'
    scrape_interval: 30s

  # Base de données PostgreSQL
  - job_name: 'postgres'
    static_configs:
      - targets: ['db:5432']
    scrape_interval: 30s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # Nginx Proxy
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-proxy:80']
    metrics_path: '/nginx-health'
    scrape_interval: 30s
