version: '3.8'

services:
  # Application EcoTask (Production)
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: ecotask_app_prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    environment:
      - APP_ENV=prod
      - APP_DEBUG=0
      - DATABASE_URL=${DATABASE_URL}
      - MAILER_DSN=${MAILER_DSN}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - APP_SECRET=${APP_SECRET}
    depends_on:
      - db
      - redis
    networks:
      - ecotask_network
    volumes:
      - ./docker/nginx/ssl:/etc/nginx/ssl
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Base de données PostgreSQL (Production)
  db:
    image: postgres:15-alpine
    container_name: ecotask_db_prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - db_data_prod:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ecotask_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis (Production)
  redis:
    image: redis:7-alpine
    container_name: ecotask_redis_prod
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data_prod:/data
    networks:
      - ecotask_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Proxy (Load Balancer)
  nginx-proxy:
    image: nginx:alpine
    container_name: ecotask_proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/proxy.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - ecotask_network

  # Monitoring avec Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: ecotask_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ecotask_network

  # Grafana pour les dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: ecotask_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - ecotask_network

volumes:
  db_data_prod:
    driver: local
  redis_data_prod:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  ecotask_network:
    driver: bridge
