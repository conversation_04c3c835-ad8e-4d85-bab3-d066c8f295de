{% extends 'base.html.twig' %}

{% block title %}Tableau de bord - EcoTask{% endblock %}

{% block body %}
<!-- Hero Section -->
<div class="relative overflow-hidden bg-gradient-to-r from-eco-green-600 to-eco-blue-600 rounded-2xl mb-8 p-8 text-white">
    <div class="absolute inset-0 bg-black opacity-10"></div>
    <div class="relative z-10">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
                <h1 class="text-4xl font-bold mb-2 animate-fade-in">
                    Tableau de bord EcoTask
                </h1>
                <p class="text-xl opacity-90 mb-4">
                    Gérez vos tâches tout en préservant l'environnement 🌱
                </p>
                <div class="flex items-center space-x-4">
                    <div class="bg-white/20 rounded-lg px-4 py-2">
                        <span class="text-sm font-medium">Impact total : {{ totalCo2|number_format(2) }} kg CO₂</span>
                    </div>
                </div>
            </div>
            <div class="mt-6 md:mt-0">
                <a href="{{ path('app_task_new') }}" class="inline-flex items-center px-6 py-3 bg-white text-eco-green-600 font-semibold rounded-lg hover:bg-gray-100 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Nouvelle tâche
                </a>
            </div>
        </div>
    </div>
    <div class="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full animate-float"></div>
    <div class="absolute bottom-0 left-0 -mb-8 -ml-8 w-24 h-24 bg-white/5 rounded-full animate-float" style="animation-delay: 2s;"></div>
</div>

<!-- Statistiques principales -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total des tâches -->
    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100 hover:border-blue-200 group">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-500 text-sm font-medium uppercase tracking-wide">Tâches totales</p>
                <p class="text-3xl font-bold text-gray-900 mt-2">{{ totalTasks }}</p>
                <div class="flex items-center mt-2">
                    <span class="text-sm text-gray-600">Toutes les tâches</span>
                </div>
            </div>
            <div class="bg-blue-100 rounded-lg p-3 group-hover:bg-blue-200 transition-colors duration-200">
                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Tâches terminées -->
    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100 hover:border-green-200 group">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-500 text-sm font-medium uppercase tracking-wide">Terminées</p>
                <p class="text-3xl font-bold text-gray-900 mt-2">{{ completedTasks }}</p>
                <div class="flex items-center mt-2">
                    <span class="text-sm text-green-600 font-medium">
                        {% if totalTasks > 0 %}
                            {{ (completedTasks / totalTasks * 100)|number_format(0) }}% complété
                        {% else %}
                            0% complété
                        {% endif %}
                    </span>
                </div>
            </div>
            <div class="bg-green-100 rounded-lg p-3 group-hover:bg-green-200 transition-colors duration-200">
                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Tâches en cours -->
    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100 hover:border-yellow-200 group">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-500 text-sm font-medium uppercase tracking-wide">En cours</p>
                <p class="text-3xl font-bold text-gray-900 mt-2">{{ inProgressTasks }}</p>
                <div class="flex items-center mt-2">
                    <span class="text-sm text-yellow-600 font-medium">En progression</span>
                </div>
            </div>
            <div class="bg-yellow-100 rounded-lg p-3 group-hover:bg-yellow-200 transition-colors duration-200">
                <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Impact CO2 -->
    <div class="bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 text-white group hover:scale-105">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-emerald-100 text-sm font-medium uppercase tracking-wide">Impact CO₂</p>
                <p class="text-3xl font-bold mt-2">{{ totalCo2|number_format(1) }}</p>
                <div class="flex items-center mt-2">
                    <span class="text-sm text-emerald-100">kg émis au total</span>
                </div>
            </div>
            <div class="bg-white/20 rounded-lg p-3 group-hover:bg-white/30 transition-colors duration-200">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Analyse environnementale -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Émissions CO2 par type -->
    <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <svg class="w-5 h-5 mr-2 text-eco-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                Émissions CO₂ par type
            </h3>
        </div>
        <div class="space-y-4">
            {% for type, emission in co2ByType %}
                {% set percentage = totalCo2 > 0 ? (emission / totalCo2 * 100) : 0 %}
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center space-x-2">
                            {% if type == 'office_light' %}
                                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                <span class="text-sm font-medium text-gray-700">Bureautique légère</span>
                            {% elseif type == 'technical' %}
                                <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                <span class="text-sm font-medium text-gray-700">Tâches techniques</span>
                            {% else %}
                                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                <span class="text-sm font-medium text-gray-700">Forte intensité</span>
                            {% endif %}
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-semibold text-gray-900">{{ emission|number_format(2) }} kg</span>
                            <span class="text-xs text-gray-500 ml-1">({{ percentage|number_format(1) }}%)</span>
                        </div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="h-2 rounded-full {{ type == 'office_light' ? 'bg-green-500' : (type == 'technical' ? 'bg-blue-500' : 'bg-red-500') }}" style="width: {{ percentage }}%"></div>
                    </div>
                </div>
            {% endfor %}
        </div>
        {% if totalCo2 > 0 %}
            <div class="mt-6 p-4 bg-eco-green-50 rounded-lg">
                <p class="text-sm text-eco-green-700">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Privilégiez les tâches de bureautique légère pour réduire votre empreinte carbone.
                </p>
            </div>
        {% endif %}
    </div>

    <!-- Top projets -->
    <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <svg class="w-5 h-5 mr-2 text-eco-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z"></path>
                </svg>
                Projets actifs
            </h3>
            <a href="{{ path('app_project_index') }}" class="text-eco-blue-600 hover:text-eco-blue-700 text-sm font-medium">
                Voir tous →
            </a>
        </div>
        {% if projectsWithCo2|length > 0 %}
            <div class="space-y-4">
                {% for item in projectsWithCo2|slice(0, 5) %}
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900">{{ item.project.name }}</h4>
                            <div class="flex items-center space-x-4 mt-1">
                                <span class="text-sm text-gray-500">{{ item.taskCount }} tâches</span>
                                <span class="text-sm text-gray-500">•</span>
                                <span class="text-sm text-gray-500">{{ item.project.members|length }} membres</span>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-emerald-500 to-teal-600 text-white">
                                {{ item.co2|number_format(1) }} kg CO₂
                            </span>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-8">
                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                </svg>
                <p class="text-gray-500 text-sm">Aucun projet trouvé</p>
                <a href="{{ path('app_project_new') }}" class="inline-flex items-center mt-3 px-4 py-2 bg-eco-green-600 text-white text-sm font-medium rounded-lg hover:bg-eco-green-700 transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Créer un projet
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Activité récente -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Tâches récentes -->
    <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Activité récente
            </h3>
            <a href="{{ path('app_task_index') }}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                Voir toutes →
            </a>
        </div>
        {% if recentTasks|length > 0 %}
            <div class="space-y-4">
                {% for task in recentTasks %}
                    <div class="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <div class="flex-shrink-0">
                            {% if task.status == 'done' %}
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            {% elseif task.status == 'in_progress' %}
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            {% else %}
                                <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            {% endif %}
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">{{ task.title }}</p>
                            <p class="text-sm text-gray-500">{{ task.project.name }}</p>
                            <div class="flex items-center space-x-2 mt-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ task.status == 'done' ? 'bg-green-100 text-green-800' : (task.status == 'in_progress' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                                    {{ task.status == 'done' ? 'Terminée' : (task.status == 'in_progress' ? 'En cours' : 'À faire') }}
                                </span>
                                <span class="text-xs text-gray-500">{{ task.co2Emission|number_format(2) }} kg CO₂</span>
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <a href="{{ path('app_task_show', {'id': task.id}) }}" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-8">
                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <p class="text-gray-500 text-sm">Aucune tâche récente</p>
            </div>
        {% endif %}
    </div>

    <!-- Tâches en retard -->
    <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <svg class="w-5 h-5 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                Tâches en retard
            </h3>
        </div>
        {% if overdueTasks|length > 0 %}
            <div class="space-y-4">
                {% for task in overdueTasks %}
                    <div class="flex items-start space-x-4 p-4 bg-red-50 rounded-lg border border-red-200">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">{{ task.title }}</p>
                            <p class="text-sm text-gray-500">{{ task.project.name }}</p>
                            <div class="flex items-center space-x-2 mt-2">
                                <span class="text-xs text-red-600 font-medium">
                                    Échéance dépassée : {{ task.dueDate|date('d/m/Y') }}
                                </span>
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <a href="{{ path('app_task_edit', {'id': task.id}) }}" class="inline-flex items-center px-3 py-1 border border-red-300 text-xs font-medium rounded-md text-red-700 bg-white hover:bg-red-50 transition-colors duration-200">
                                Corriger
                            </a>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-8">
                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <p class="text-green-600 font-medium text-sm">Aucune tâche en retard !</p>
                <p class="text-gray-500 text-xs mt-1">Excellent travail ! 🎉</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
