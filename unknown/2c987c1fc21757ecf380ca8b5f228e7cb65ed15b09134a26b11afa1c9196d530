services:
  # Application EcoTask
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: ecotask_app
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
      - ./docker/php/php.ini:/usr/local/etc/php/conf.d/custom.ini
    environment:
      - APP_ENV=dev
      - APP_DEBUG=1
      - DATABASE_URL=mysql://ecotask:ecotask_password@db:3306/ecotask_db
      - MAILER_DSN=smtp://mailhog:1025
      - MESSENGER_TRANSPORT_DSN=doctrine://default
    depends_on:
      - db
      - redis
      - mailhog
    networks:
      - ecotask_network

  # Base de données MySQL
  db:
    image: mysql:8.0
    container_name: ecotask_db
    restart: unless-stopped
    ports:
      - "3307:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: ecotask_db
      MYSQL_USER: ecotask
      MYSQL_PASSWORD: ecotask_password
    volumes:
      - db_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/custom.cnf
    networks:
      - ecotask_network

  # Redis pour le cache et les sessions
  redis:
    image: redis:7-alpine
    container_name: ecotask_redis
    restart: unless-stopped
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass redis_password
    networks:
      - ecotask_network

  # MailHog pour les emails de développement
  mailhog:
    image: mailhog/mailhog:latest
    container_name: ecotask_mailhog
    restart: unless-stopped
    ports:
      - "8025:8025"  # Interface web
      - "1025:1025"  # SMTP
    networks:
      - ecotask_network

  # Adminer pour la gestion de base de données
  adminer:
    image: adminer:latest
    container_name: ecotask_adminer
    restart: unless-stopped
    ports:
      - "8081:8080"
    environment:
      ADMINER_DEFAULT_SERVER: db
    depends_on:
      - db
    networks:
      - ecotask_network



volumes:
  db_data:
    driver: local
  redis_data:
    driver: local

networks:
  ecotask_network:
    driver: bridge
