#!/bin/bash

# Script de nettoyage Docker pour EcoTask
# Usage: ./scripts/cleanup-docker.sh [--full]

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Fonction de nettoyage standard
cleanup_standard() {
    log_info "Nettoyage standard des conteneurs EcoTask..."

    # Arrêt de tous les services avec toutes les configurations
    log_info "Arrêt des services de développement..."
    sudo docker-compose down --remove-orphans 2>/dev/null || true

    log_info "Arrêt des services de test..."
    sudo docker-compose -f docker-compose.test.yml down --remove-orphans 2>/dev/null || true

    log_info "Arrêt des services de production..."
    sudo docker-compose -f docker-compose.prod.yml down --remove-orphans 2>/dev/null || true

    # Suppression des conteneurs arrêtés
    log_info "Suppression des conteneurs arrêtés..."
    sudo docker container prune -f

    # Suppression des images non utilisées
    log_info "Suppression des images non utilisées..."
    sudo docker image prune -f

    # Suppression des réseaux non utilisés
    log_info "Suppression des réseaux non utilisés..."
    sudo docker network prune -f

    log_success "Nettoyage standard terminé"
}

# Fonction de nettoyage complet
cleanup_full() {
    log_warning "Nettoyage complet - ATTENTION: Supprime TOUTES les données !"

    # Nettoyage standard d'abord
    cleanup_standard

    # Suppression des volumes (DONNÉES PERDUES)
    log_warning "Suppression des volumes de données..."
    sudo docker volume ls -q | grep ecotask | xargs -r sudo docker volume rm

    # Nettoyage système complet
    log_info "Nettoyage système Docker complet..."
    sudo docker system prune -af --volumes

    log_success "Nettoyage complet terminé"
}

# Fonction d'affichage de l'état
show_status() {
    echo ""
    log_info "État actuel des ressources Docker EcoTask:"
    echo ""

    echo "📦 Conteneurs:"
    sudo docker ps -a --filter "name=ecotask" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" || echo "Aucun conteneur EcoTask"

    echo ""
    echo "🌐 Réseaux:"
    sudo docker network ls --filter "name=ecotask" --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}" || echo "Aucun réseau EcoTask"

    echo ""
    echo "💾 Volumes:"
    sudo docker volume ls --filter "name=ecotask" --format "table {{.Name}}\t{{.Driver}}\t{{.Size}}" || echo "Aucun volume EcoTask"

    echo ""
    echo "🖼️ Images:"
    sudo docker images --filter "reference=*ecotask*" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" || echo "Aucune image EcoTask"
}

# Fonction d'aide
show_help() {
    echo "🧹 Script de nettoyage Docker EcoTask"
    echo ""
    echo "Usage:"
    echo "  ./scripts/cleanup-docker.sh              # Nettoyage standard (garde les données)"
    echo "  ./scripts/cleanup-docker.sh --full       # Nettoyage complet (supprime TOUT)"
    echo "  ./scripts/cleanup-docker.sh --status     # Affiche l'état actuel"
    echo "  ./scripts/cleanup-docker.sh --help       # Affiche cette aide"
    echo ""
    echo "Options:"
    echo "  --full      Supprime TOUT (conteneurs, images, volumes, données)"
    echo "  --status    Affiche l'état des ressources Docker EcoTask"
    echo "  --help      Affiche cette aide"
    echo ""
    echo "⚠️  ATTENTION: --full supprime définitivement toutes les données !"
}

# Fonction de confirmation
confirm_action() {
    local action=$1
    echo ""
    log_warning "Vous êtes sur le point d'effectuer: $action"
    read -p "Êtes-vous sûr ? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Opération annulée"
        exit 0
    fi
}

# Fonction principale
main() {
    echo "🐳 EcoTask Docker Cleanup Script"
    echo "================================="

    case "${1:-}" in
        --full)
            confirm_action "NETTOYAGE COMPLET (supprime toutes les données)"
            cleanup_full
            ;;
        --status)
            show_status
            ;;
        --help)
            show_help
            ;;
        "")
            cleanup_standard
            ;;
        *)
            log_error "Option inconnue: $1"
            show_help
            exit 1
            ;;
    esac

    echo ""
    log_info "État final:"
    show_status
}

# Gestion des signaux
trap 'log_error "Nettoyage interrompu"; exit 1' INT TERM

# Exécution
main "$@"
