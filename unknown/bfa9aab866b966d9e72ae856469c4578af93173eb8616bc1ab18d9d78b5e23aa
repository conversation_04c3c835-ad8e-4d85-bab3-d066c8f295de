services:
  # Application EcoTask (version test avec PHP 8.3)
  app:
    build:
      context: .
      dockerfile: Dockerfile.test
    container_name: ecotask_app_test
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
    environment:
      - APP_ENV=dev
      - APP_DEBUG=1
      - DATABASE_URL=mysql://ecotask:ecotask_password@db:3306/ecotask_db
    depends_on:
      - db
    networks:
      - ecotask_network

  # Base de données MySQL
  db:
    image: mysql:8.0
    container_name: ecotask_db_test
    restart: unless-stopped
    ports:
      - "3307:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: ecotask_db
      MYSQL_USER: ecotask
      MYSQL_PASSWORD: ecotask_password
    networks:
      - ecotask_network

networks:
  ecotask_network:
    driver: bridge
