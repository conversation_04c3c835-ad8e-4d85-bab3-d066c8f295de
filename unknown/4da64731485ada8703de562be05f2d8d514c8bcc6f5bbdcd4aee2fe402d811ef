#!/bin/bash

# Pre-commit hook pour EcoTask
# Valide le code avant chaque commit

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier si nous sommes dans un environnement Docker
check_docker_environment() {
    if ! docker ps >/dev/null 2>&1; then
        log_error "Docker n'est pas disponible"
        log_info "Démarrez l'environnement Docker avec: make quick-start"
        exit 1
    fi
    
    if ! docker ps | grep -q "ecotask_app"; then
        log_warning "Conteneur EcoTask non démarré"
        log_info "Démarrage automatique..."
        make quick-start
    fi
}

# Vérifier la syntaxe PHP
check_php_syntax() {
    log_info "Vérification de la syntaxe PHP..."
    
    local php_files=$(git diff --cached --name-only --diff-filter=ACM | grep '\.php$' || true)
    
    if [ -z "$php_files" ]; then
        log_info "Aucun fichier PHP modifié"
        return 0
    fi
    
    local syntax_errors=0
    
    for file in $php_files; do
        if [ -f "$file" ]; then
            if ! php -l "$file" >/dev/null 2>&1; then
                log_error "Erreur de syntaxe dans $file"
                php -l "$file"
                ((syntax_errors++))
            fi
        fi
    done
    
    if [ $syntax_errors -gt 0 ]; then
        log_error "$syntax_errors erreur(s) de syntaxe PHP détectée(s)"
        return 1
    fi
    
    log_success "Syntaxe PHP valide"
    return 0
}

# Vérifier le style de code
check_code_style() {
    log_info "Vérification du style de code..."
    
    local php_files=$(git diff --cached --name-only --diff-filter=ACM | grep '\.php$' || true)
    
    if [ -z "$php_files" ]; then
        log_info "Aucun fichier PHP modifié"
        return 0
    fi
    
    # Vérifier avec PHP CS Fixer
    if docker exec ecotask_app_test vendor/bin/php-cs-fixer fix --dry-run --diff >/dev/null 2>&1; then
        log_success "Style de code conforme"
        return 0
    else
        log_error "Style de code non conforme"
        log_info "Correction automatique disponible avec: make cs-fix"
        
        # Proposer la correction automatique
        read -p "Voulez-vous corriger automatiquement le style ? (y/N): " -n 1 -r
        echo ""
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "Correction automatique du style..."
            docker exec ecotask_app_test vendor/bin/php-cs-fixer fix
            
            # Re-ajouter les fichiers modifiés
            for file in $php_files; do
                git add "$file"
            done
            
            log_success "Style de code corrigé et fichiers re-ajoutés"
            return 0
        else
            return 1
        fi
    fi
}

# Analyse statique avec PHPStan
check_static_analysis() {
    log_info "Analyse statique avec PHPStan..."
    
    if docker exec ecotask_app_test vendor/bin/phpstan analyse --memory-limit=1G >/dev/null 2>&1; then
        log_success "Analyse statique réussie"
        return 0
    else
        log_error "Erreurs détectées par PHPStan"
        log_info "Détails disponibles avec: make phpstan"
        return 1
    fi
}

# Exécuter les tests
run_tests() {
    log_info "Exécution des tests..."
    
    if docker exec ecotask_app_test php bin/phpunit >/dev/null 2>&1; then
        log_success "Tous les tests passent"
        return 0
    else
        log_error "Des tests échouent"
        log_info "Détails disponibles avec: make test"
        return 1
    fi
}

# Vérifier les fichiers de configuration
check_config_files() {
    log_info "Vérification des fichiers de configuration..."
    
    local config_files=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(yaml|yml|json|xml)$' || true)
    
    if [ -z "$config_files" ]; then
        log_info "Aucun fichier de configuration modifié"
        return 0
    fi
    
    local config_errors=0
    
    for file in $config_files; do
        if [ -f "$file" ]; then
            case "$file" in
                *.yaml|*.yml)
                    if ! python3 -c "import yaml; yaml.safe_load(open('$file'))" >/dev/null 2>&1; then
                        log_error "Erreur de syntaxe YAML dans $file"
                        ((config_errors++))
                    fi
                    ;;
                *.json)
                    if ! python3 -c "import json; json.load(open('$file'))" >/dev/null 2>&1; then
                        log_error "Erreur de syntaxe JSON dans $file"
                        ((config_errors++))
                    fi
                    ;;
            esac
        fi
    done
    
    if [ $config_errors -gt 0 ]; then
        log_error "$config_errors erreur(s) de configuration détectée(s)"
        return 1
    fi
    
    log_success "Fichiers de configuration valides"
    return 0
}

# Vérifier la taille des fichiers
check_file_sizes() {
    log_info "Vérification de la taille des fichiers..."
    
    local large_files=$(git diff --cached --name-only --diff-filter=ACM | xargs -I {} find {} -size +1M 2>/dev/null || true)
    
    if [ -n "$large_files" ]; then
        log_warning "Fichiers volumineux détectés (>1MB):"
        echo "$large_files"
        
        read -p "Voulez-vous continuer malgré tout ? (y/N): " -n 1 -r
        echo ""
        
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_error "Commit annulé à cause de fichiers volumineux"
            return 1
        fi
    fi
    
    log_success "Taille des fichiers acceptable"
    return 0
}

# Fonction principale
main() {
    echo "🔍 Pre-commit Hook EcoTask"
    echo "=========================="
    
    local failed_checks=0
    local total_checks=0
    
    # Vérifications rapides (sans Docker)
    echo ""
    echo "📋 Vérifications rapides:"
    
    ((total_checks++))
    check_php_syntax || ((failed_checks++))
    
    ((total_checks++))
    check_config_files || ((failed_checks++))
    
    ((total_checks++))
    check_file_sizes || ((failed_checks++))
    
    # Vérifications avec Docker
    echo ""
    echo "🐳 Vérifications avec Docker:"
    
    check_docker_environment
    
    ((total_checks++))
    check_code_style || ((failed_checks++))
    
    ((total_checks++))
    check_static_analysis || ((failed_checks++))
    
    ((total_checks++))
    run_tests || ((failed_checks++))
    
    # Résumé
    echo ""
    echo "📊 Résumé:"
    echo "=========="
    
    local passed_checks=$((total_checks - failed_checks))
    
    if [ $failed_checks -eq 0 ]; then
        log_success "Toutes les vérifications sont passées ! ($passed_checks/$total_checks)"
        echo ""
        echo "✅ Commit autorisé"
        exit 0
    else
        log_error "$failed_checks vérification(s) échouée(s) sur $total_checks"
        echo ""
        echo "❌ Commit bloqué"
        echo ""
        echo "💡 Conseils:"
        echo "  - Corrigez les erreurs signalées"
        echo "  - Utilisez 'make pre-commit' pour tester localement"
        echo "  - Utilisez 'git commit --no-verify' pour forcer (non recommandé)"
        exit 1
    fi
}

# Gestion des signaux
trap 'log_error "Pre-commit hook interrompu"; exit 1' INT TERM

# Exécution
main "$@"
