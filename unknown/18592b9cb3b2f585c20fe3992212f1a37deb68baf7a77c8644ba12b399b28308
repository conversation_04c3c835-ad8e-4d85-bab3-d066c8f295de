#!/bin/bash

# Script de test complet de la conteneurisation EcoTask
# Usage: ./scripts/test-docker.sh [environment]

set -e

ENVIRONMENT=${1:-development}
PROJECT_NAME="ecotask"

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test de connectivité HTTP
test_http() {
    local url=$1
    local expected_code=${2:-200}
    local description=$3
    
    log_info "Test: $description"
    
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" || echo "000")
    
    if [ "$response_code" = "$expected_code" ]; then
        log_success "$description - OK ($response_code)"
        return 0
    else
        log_error "$description - FAILED (got $response_code, expected $expected_code)"
        return 1
    fi
}

# Test de service Docker
test_service() {
    local service_name=$1
    local description=$2
    
    log_info "Test: $description"
    
    if docker-compose ps | grep -q "$service_name.*Up"; then
        log_success "$description - OK (service running)"
        return 0
    else
        log_error "$description - FAILED (service not running)"
        return 1
    fi
}

# Test de base de données
test_database() {
    log_info "Test: Base de données MySQL"
    
    if docker exec ecotask_db mysql -u ecotask -pecotask_password -e "SELECT 1;" ecotask_db >/dev/null 2>&1; then
        log_success "Base de données MySQL - OK"
        return 0
    else
        log_error "Base de données MySQL - FAILED"
        return 1
    fi
}

# Test de Redis
test_redis() {
    log_info "Test: Cache Redis"
    
    if docker exec ecotask_redis redis-cli -a redis_password ping >/dev/null 2>&1; then
        log_success "Cache Redis - OK"
        return 0
    else
        log_error "Cache Redis - FAILED"
        return 1
    fi
}

# Test de l'API de santé
test_health_api() {
    log_info "Test: API de santé"
    
    local health_response=$(curl -s http://localhost:8080/health)
    local status=$(echo "$health_response" | jq -r '.status' 2>/dev/null || echo "error")
    
    if [ "$status" = "healthy" ]; then
        log_success "API de santé - OK"
        return 0
    else
        log_error "API de santé - FAILED (status: $status)"
        return 1
    fi
}

# Fonction principale de test
run_tests() {
    echo "🧪 Tests de conteneurisation EcoTask - Environnement: $ENVIRONMENT"
    echo "=================================================================="
    
    local failed_tests=0
    local total_tests=0
    
    # Tests des services Docker
    echo ""
    echo "📦 Tests des services Docker:"
    
    ((total_tests++))
    test_service "ecotask_app" "Service Application" || ((failed_tests++))
    
    ((total_tests++))
    test_service "ecotask_db" "Service Base de données" || ((failed_tests++))
    
    ((total_tests++))
    test_service "ecotask_redis" "Service Redis" || ((failed_tests++))
    
    ((total_tests++))
    test_service "ecotask_adminer" "Service Adminer" || ((failed_tests++))
    
    ((total_tests++))
    test_service "ecotask_mailhog" "Service MailHog" || ((failed_tests++))
    
    # Tests de connectivité
    echo ""
    echo "🌐 Tests de connectivité HTTP:"
    
    ((total_tests++))
    test_http "http://localhost:8080" 200 "Page d'accueil" || ((failed_tests++))
    
    ((total_tests++))
    test_http "http://localhost:8080/health" 200 "API de santé" || ((failed_tests++))
    
    ((total_tests++))
    test_http "http://localhost:8080/task/" 200 "Liste des tâches" || ((failed_tests++))
    
    ((total_tests++))
    test_http "http://localhost:8080/project/" 200 "Liste des projets" || ((failed_tests++))
    
    ((total_tests++))
    test_http "http://localhost:8081" 200 "Interface Adminer" || ((failed_tests++))
    
    ((total_tests++))
    test_http "http://localhost:8025" 200 "Interface MailHog" || ((failed_tests++))
    
    # Tests des bases de données
    echo ""
    echo "🗄️ Tests des bases de données:"
    
    ((total_tests++))
    test_database || ((failed_tests++))
    
    ((total_tests++))
    test_redis || ((failed_tests++))
    
    # Tests de l'API
    echo ""
    echo "🔍 Tests de l'API:"
    
    ((total_tests++))
    test_health_api || ((failed_tests++))
    
    # Résumé
    echo ""
    echo "📊 Résumé des tests:"
    echo "==================="
    
    local passed_tests=$((total_tests - failed_tests))
    
    if [ $failed_tests -eq 0 ]; then
        log_success "Tous les tests sont passés ! ($passed_tests/$total_tests)"
        echo ""
        echo "🎉 La conteneurisation EcoTask est entièrement fonctionnelle !"
        return 0
    else
        log_error "$failed_tests test(s) échoué(s) sur $total_tests"
        echo ""
        echo "❌ Des problèmes ont été détectés dans la conteneurisation."
        return 1
    fi
}

# Vérification des prérequis
check_requirements() {
    log_info "Vérification des prérequis..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker n'est pas installé"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose n'est pas installé"
        exit 1
    fi
    
    if ! command -v curl &> /dev/null; then
        log_error "curl n'est pas installé"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq n'est pas installé (tests API limités)"
    fi
    
    log_success "Prérequis OK"
}

# Fonction principale
main() {
    check_requirements
    run_tests
}

# Gestion des signaux
trap 'log_error "Tests interrompus"; exit 1' INT TERM

# Exécution
main "$@"
