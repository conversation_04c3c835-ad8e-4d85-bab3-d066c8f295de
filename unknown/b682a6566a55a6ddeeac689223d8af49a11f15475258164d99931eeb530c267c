# 🐳 EcoTask - Environnements Docker

## 📋 Vue d'ensemble des configurations

EcoTask utilise **3 configurations Docker** distinctes pour différents besoins :

```
📁 Configurations Docker
├── 🧪 docker-compose.test.yml     → Tests et validation
├── 💻 docker-compose.yml          → Développement complet  
└── 🚀 docker-compose.prod.yml     → Production
```

---

## 🧪 **Test - `docker-compose.test.yml`**

### 🎯 **Objectif :**
Configuration **minimaliste** pour tester la compatibilité et valider les fonctionnalités de base.

### 📦 **Services inclus :**
- ✅ **App** : PHP 8.3 + Nginx + Supervisor
- ✅ **Database** : MySQL 8.0
- ❌ <PERSON><PERSON>, Adminer, MailHog (exclus pour simplifier)

### 🚀 **Utilisation :**
```bash
# Démarrage rapide pour tests
sudo docker-compose -f docker-compose.test.yml up -d

# URLs disponibles
http://localhost:8080        # Application
http://localhost:8080/health # API de santé
```

### ✅ **Avantages :**
- **Démarrage rapide** (~20 secondes)
- **Ressources minimales** (2 conteneurs seulement)
- **Idéal pour validation** et tests de compatibilité
- **Debugging simplifié**

### 📝 **Quand l'utiliser :**
- ✅ Tests de compatibilité PHP
- ✅ Validation des migrations
- ✅ CI/CD pipelines
- ✅ Debugging rapide

---

## 💻 **Développement - `docker-compose.yml`**

### 🎯 **Objectif :**
Configuration **complète** pour le développement quotidien avec tous les services.

### 📦 **Services inclus :**
- ✅ **App** : PHP 8.3 + Nginx + Supervisor + Xdebug
- ✅ **Database** : MySQL 8.0 avec configuration optimisée
- ✅ **Redis** : Cache et sessions
- ✅ **Adminer** : Interface de gestion BDD
- ✅ **MailHog** : Capture d'emails pour tests
- ✅ **Node** : Build des assets Tailwind CSS

### 🚀 **Utilisation :**
```bash
# Démarrage complet pour développement
sudo docker-compose up -d

# URLs disponibles
http://localhost:8080        # Application
http://localhost:8081        # Adminer (gestion BDD)
http://localhost:8025        # MailHog (emails)
http://localhost:8080/health # API de santé
```

### ✅ **Avantages :**
- **Environnement complet** avec tous les services
- **Hot reload** pour le développement
- **Debugging avancé** avec Xdebug
- **Gestion emails** avec MailHog
- **Interface BDD** avec Adminer

### 📝 **Quand l'utiliser :**
- ✅ Développement quotidien
- ✅ Tests d'intégration complets
- ✅ Développement de fonctionnalités emails
- ✅ Gestion avancée de la base de données

---

## 🚀 **Production - `docker-compose.prod.yml`**

### 🎯 **Objectif :**
Configuration **optimisée** pour la production avec monitoring et sécurité.

### 📦 **Services inclus :**
- ✅ **App** : PHP 8.3 optimisé (sans Xdebug)
- ✅ **Database** : PostgreSQL 15 (plus robuste que MySQL)
- ✅ **Redis** : Cache avec authentification
- ✅ **Nginx Proxy** : Load balancer et HTTPS
- ✅ **Prometheus** : Collecte de métriques
- ✅ **Grafana** : Dashboards de monitoring

### 🚀 **Utilisation :**
```bash
# Déploiement production
sudo docker-compose -f docker-compose.prod.yml up -d

# URLs disponibles
http://localhost:80          # Application (HTTP)
https://localhost:443        # Application (HTTPS)
http://localhost:3000        # Grafana (monitoring)
http://localhost:9090        # Prometheus (métriques)
```

### ✅ **Avantages :**
- **Performance optimisée** (OPcache, pas de Xdebug)
- **Sécurité renforcée** (HTTPS, authentification)
- **Monitoring complet** (métriques, logs, alertes)
- **Scalabilité** (load balancer, réplication)
- **Base robuste** (PostgreSQL vs MySQL)

### 📝 **Quand l'utiliser :**
- ✅ Déploiement en production
- ✅ Tests de performance
- ✅ Validation avant mise en ligne
- ✅ Monitoring et observabilité

---

## 🔄 **Workflow recommandé**

### 1. **Développement quotidien :**
```bash
# Utiliser la configuration complète
sudo docker-compose up -d
```

### 2. **Tests rapides :**
```bash
# Utiliser la configuration test
sudo docker-compose -f docker-compose.test.yml up -d
```

### 3. **Validation avant production :**
```bash
# Tester avec la configuration production
sudo docker-compose -f docker-compose.prod.yml up -d
```

---

## 📊 **Comparaison des configurations**

| Aspect | Test | Développement | Production |
|--------|------|---------------|------------|
| **Services** | 2 | 6 | 6 |
| **Démarrage** | ~20s | ~45s | ~60s |
| **Mémoire** | ~200MB | ~500MB | ~800MB |
| **Debugging** | Basique | Avancé (Xdebug) | Logs |
| **Base de données** | MySQL | MySQL | PostgreSQL |
| **Cache** | ❌ | Redis | Redis |
| **Monitoring** | Health API | Logs | Prometheus+Grafana |
| **Sécurité** | Basique | Développement | Production |
| **Performance** | Standard | Standard | Optimisée |

---

## 🎯 **Recommandations d'usage**

### 🧪 **Utilisez `docker-compose.test.yml` pour :**
- ✅ Premiers tests après installation
- ✅ Validation de compatibilité
- ✅ CI/CD pipelines
- ✅ Debugging rapide d'un problème
- ✅ Tests de migration de base de données

### 💻 **Utilisez `docker-compose.yml` pour :**
- ✅ Développement quotidien
- ✅ Développement de nouvelles fonctionnalités
- ✅ Tests d'intégration complets
- ✅ Développement d'emails et notifications
- ✅ Gestion avancée de la base de données

### 🚀 **Utilisez `docker-compose.prod.yml` pour :**
- ✅ Tests de performance
- ✅ Validation avant déploiement
- ✅ Déploiement en production
- ✅ Monitoring et observabilité
- ✅ Tests de charge et scalabilité

---

## 🔧 **Migration entre environnements**

### De Test vers Développement :
```bash
# Arrêter test
sudo docker-compose -f docker-compose.test.yml down

# Démarrer développement
sudo docker-compose up -d
```

### De Développement vers Production :
```bash
# Arrêter développement
sudo docker-compose down

# Démarrer production
sudo docker-compose -f docker-compose.prod.yml up -d
```

### Sauvegarde des données :
```bash
# Sauvegarder avant changement
sudo docker exec ecotask_db_test mysqldump -u ecotask -pecotask_password ecotask_db > backup.sql

# Restaurer après changement
sudo docker exec -i ecotask_db mysql -u ecotask -pecotask_password ecotask_db < backup.sql
```

---

## 💡 **Points clés à retenir**

### 🎯 **Stratégie actuelle :**
1. **Validation** → `docker-compose.test.yml` ✅ (testé et fonctionnel)
2. **Développement** → `docker-compose.yml` (à tester)
3. **Production** → `docker-compose.prod.yml` (préparé)

### 🚀 **Prochaines étapes :**
1. Tester `docker-compose.yml` complet
2. Valider tous les services (Redis, Adminer, MailHog)
3. Préparer la configuration production
4. Mettre en place le monitoring

---

*Chaque configuration a son rôle spécifique dans le cycle de développement d'EcoTask !*
