# 📢 Guide d'Intégration Slack - EcoTask

## 🎯 Vue d'ensemble

Ce guide explique comment configurer les notifications Slack pour les déploiements EcoTask.

## 🔧 Configuration Slack

### 1. **Création d'une App Slack**

1. Allez sur https://api.slack.com/apps
2. Cliquez sur "Create New App"
3. Choi<PERSON><PERSON>z "From scratch"
4. Nommez votre app "EcoTask CI/CD"
5. Sélectionnez votre workspace

### 2. **Configuration des Webhooks**

1. Dans votre app, allez dans "Incoming Webhooks"
2. Activez "Activate Incoming Webhooks"
3. C<PERSON>z sur "Add New Webhook to Workspace"
4. Sélectionnez le canal (ex: #deployments)
5. Copiez l'URL du webhook

### 3. **Configuration GitHub Secrets**

1. Allez dans votre repo GitHub
2. Settings → Secrets and variables → Actions
3. C<PERSON>z "New repository secret"
4. Nom: `SLACK_WEBHOOK_URL`
5. Valeur: L'URL copiée de Slack

## 🔄 Réactivation des Notifications

### **Remplacer les notifications console par Slack**

Dans `.github/workflows/deploy.yml`, remplacez :

```yaml
# Notification Console (actuelle)
- name: 📢 Notification Déploiement (Console)
  if: always()
  run: |
    echo "🎭 Déploiement Staging: ${{ job.status }}"
    echo "✅ Notification envoyée"
```

Par :

```yaml
# Notification Slack
- name: 📢 Notification Slack (Staging)
  if: always()
  uses: 8398a7/action-slack@v3
  with:
    status: ${{ job.status }}
    channel: '#deployments'
    text: |
      🎭 Déploiement Staging: ${{ job.status }}
      Branche: ${{ github.ref_name }}
      Commit: ${{ github.sha }}
      Image: ${{ needs.build-and-push.outputs.image-tag }}
  env:
    SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

## 📋 Canaux Recommandés

### **Structure des Canaux**
- `#deployments` : Déploiements staging
- `#production` : Déploiements production
- `#alerts` : Rollbacks et alertes
- `#ci-cd` : Builds et tests

### **Permissions**
- Développeurs : #deployments, #ci-cd
- DevOps : Tous les canaux
- Management : #production, #alerts

## 🎨 Personnalisation des Messages

### **Messages de Succès**
```yaml
text: |
  ✅ Déploiement réussi !
  🎯 Environnement: ${{ env.ENVIRONMENT }}
  🚀 Version: ${{ github.ref_name }}
  ⏱️ Durée: ${{ steps.deploy.outputs.duration }}
  🔗 URL: https://ecotask.example.com
```

### **Messages d'Erreur**
```yaml
text: |
  ❌ Déploiement échoué !
  🎯 Environnement: ${{ env.ENVIRONMENT }}
  🔍 Logs: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
  👥 Équipe: @channel
```

### **Messages de Rollback**
```yaml
text: |
  🔄 ROLLBACK AUTOMATIQUE
  ⚠️ Raison: Échec du déploiement
  🎯 Environnement: Production
  ✅ Version précédente restaurée
  🚨 Investigation requise: @devops-team
```

## 🔔 Notifications Avancées

### **Mentions Conditionnelles**
```yaml
text: |
  ${{ job.status == 'success' && '✅' || '❌' }} Déploiement: ${{ job.status }}
  ${{ job.status == 'failure' && '@channel' || '' }}
```

### **Threads de Suivi**
```yaml
# Utiliser thread_ts pour grouper les messages
thread_ts: ${{ steps.initial-message.outputs.ts }}
```

### **Boutons d'Action**
```yaml
attachments: |
  [
    {
      "color": "${{ job.status == 'success' && 'good' || 'danger' }}",
      "actions": [
        {
          "type": "button",
          "text": "Voir les logs",
          "url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
        }
      ]
    }
  ]
```

## 🧪 Test des Notifications

### **Test Manuel**
```bash
curl -X POST -H 'Content-type: application/json' \
  --data '{"text":"🧪 Test notification EcoTask"}' \
  YOUR_WEBHOOK_URL
```

### **Test avec GitHub Actions**
Créez un workflow de test :

```yaml
name: Test Slack
on: workflow_dispatch
jobs:
  test-slack:
    runs-on: ubuntu-latest
    steps:
      - name: Test Notification
        uses: 8398a7/action-slack@v3
        with:
          status: 'success'
          text: '🧪 Test notification EcoTask'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

## 🔒 Sécurité

### **Bonnes Pratiques**
- ✅ Utilisez des secrets GitHub
- ✅ Limitez les permissions du webhook
- ✅ Auditez les accès régulièrement
- ✅ Utilisez des canaux privés pour la production

### **Rotation des Webhooks**
- Changez les URLs tous les 6 mois
- Surveillez les accès suspects
- Documentez les changements

## 📊 Monitoring

### **Métriques à Surveiller**
- Taux de livraison des messages
- Temps de réponse Slack
- Erreurs de webhook

### **Alertes**
- Webhook indisponible
- Trop de notifications d'erreur
- Canaux non configurés

---

**Note** : Les notifications console actuelles fonctionnent parfaitement. Cette intégration Slack est optionnelle et peut être configurée plus tard selon vos besoins.
