#!/bin/sh

# Script d'initialisation pour le conteneur EcoTask
# Exécuté au démarrage du conteneur

set -e

echo "🚀 Initialisation EcoTask..."

# Attendre que la base de données soit disponible
echo "⏳ Attente de la base de données..."
timeout=60
while ! php bin/console doctrine:query:sql "SELECT 1" >/dev/null 2>&1; do
    timeout=$((timeout - 1))
    if [ $timeout -eq 0 ]; then
        echo "❌ Timeout: Base de données non disponible"
        exit 1
    fi
    echo "   Tentative de connexion... ($timeout secondes restantes)"
    sleep 1
done

echo "✅ Base de données disponible"

# Migrations de base de données
echo "🗄️ Application des migrations..."
php bin/console doctrine:migrations:migrate --no-interaction

# Cache warmup
echo "🔥 Réchauffement du cache..."
php bin/console cache:warmup --env=prod

# Permissions finales
echo "🔐 Configuration des permissions..."
chown -R www-data:www-data var/
chmod -R 775 var/

echo "✅ Initialisation terminée"

# Démarrage des services
echo "🚀 Démarrage des services..."
exec "$@"
