{% extends 'base.html.twig' %}

{% block title %}Modifier le projet - {{ project.name }} - EcoTask{% endblock %}

{% block body %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-pencil"></i> Modifier le projet</h1>
            <div>
                <a href="{{ path('app_project_show', {'id': project.id}) }}" class="btn btn-outline-info">
                    <i class="bi bi-eye"></i> Voir
                </a>
                <a href="{{ path('app_project_index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Retour à la liste
                </a>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Modification de : {{ project.name }}</h5>
            </div>
            <div class="card-body">
                {{ form_start(form) }}
                
                <div class="mb-3">
                    {{ form_label(form.name) }}
                    {{ form_widget(form.name) }}
                    {{ form_errors(form.name) }}
                </div>

                <div class="mb-3">
                    {{ form_label(form.description) }}
                    {{ form_widget(form.description) }}
                    {{ form_errors(form.description) }}
                </div>

                <div class="mb-3">
                    {{ form_label(form.members) }}
                    {{ form_widget(form.members) }}
                    {{ form_errors(form.members) }}
                    {{ form_help(form.members) }}
                </div>

                <div class="alert alert-warning">
                    <h6><i class="bi bi-exclamation-triangle"></i> Attention</h6>
                    <p class="mb-0">
                        La modification des membres du projet n'affectera pas les tâches déjà assignées.
                        Vous devrez modifier individuellement les assignations des tâches si nécessaire.
                    </p>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ path('app_project_show', {'id': project.id}) }}" class="btn btn-secondary">
                        <i class="bi bi-x"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check"></i> Sauvegarder les modifications
                    </button>
                </div>

                {{ form_end(form) }}
            </div>
        </div>
    </div>
</div>
{% endblock %}
