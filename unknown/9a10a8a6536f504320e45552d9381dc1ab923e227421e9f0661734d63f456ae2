-- Script d'initialisation PostgreSQL pour EcoTask Production

-- Création de la base de données si elle n'existe pas
-- (PostgreSQL crée automatiquement la base via POSTGRES_DB)

-- Configuration des paramètres de performance
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- Rechargement de la configuration
SELECT pg_reload_conf();

-- Création d'un utilisateur de lecture seule pour le monitoring
CREATE USER ecotask_monitor WITH PASSWORD 'monitor_password';
GRANT CONNECT ON DATABASE ecotask_prod_db TO ecotask_monitor;
GRANT USAGE ON SCHEMA public TO ecotask_monitor;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO ecotask_monitor;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO ecotask_monitor;
