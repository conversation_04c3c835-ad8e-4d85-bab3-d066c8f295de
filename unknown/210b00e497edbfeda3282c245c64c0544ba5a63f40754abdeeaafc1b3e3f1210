; Configuration PHP pour EcoTask

; Limites de mémoire et temps d'exécution
memory_limit = 256M
max_execution_time = 300
max_input_time = 300

; Upload de fichiers
upload_max_filesize = 20M
post_max_size = 20M
max_file_uploads = 20

; Sessions
session.gc_maxlifetime = 3600
session.cookie_lifetime = 0
session.cookie_secure = 0
session.cookie_httponly = 1
session.use_strict_mode = 1

; Sécurité
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off

; Logs d'erreurs
log_errors = On
error_log = /var/log/php_errors.log
display_errors = Off
display_startup_errors = Off

; OPcache (optimisation)
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 20000
opcache.validate_timestamps = 0
opcache.save_comments = 1
opcache.fast_shutdown = 1

; Realpath cache (performance)
realpath_cache_size = 4096K
realpath_cache_ttl = 600

; Date et timezone
date.timezone = Europe/Paris

; Encodage
default_charset = "UTF-8"

; Limites diverses
max_input_vars = 3000
max_input_nesting_level = 64

; Configuration pour Symfony
; Augmentation des limites pour les formulaires complexes
suhosin.post.max_vars = 3000
suhosin.request.max_vars = 3000
