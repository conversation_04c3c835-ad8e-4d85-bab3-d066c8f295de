#!/bin/bash

# Script de nettoyage du code base EcoTask
# Usage: ./scripts/cleanup-codebase.sh [--dry-run]

set -e

DRY_RUN=false

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Fonction pour exécuter ou simuler une commande
execute_or_simulate() {
    local command="$1"
    local description="$2"

    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] $description"
        log_info "[DRY RUN] Commande: $command"
    else
        log_info "$description"
        eval "$command"
        if [ $? -eq 0 ]; then
            log_success "$description - OK"
        else
            log_error "$description - FAILED"
            return 1
        fi
    fi
}

# Nettoyage des caches Symfony
cleanup_symfony_cache() {
    log_info "Nettoyage des caches Symfony..."

    execute_or_simulate "rm -rf var/cache/*" "Suppression du cache Symfony"
    execute_or_simulate "rm -rf var/log/*" "Suppression des logs Symfony"

    if [ -d "var/sessions" ]; then
        execute_or_simulate "rm -rf var/sessions/*" "Suppression des sessions"
    fi
}

# Nettoyage des fichiers temporaires
cleanup_temp_files() {
    log_info "Nettoyage des fichiers temporaires..."

    # Fichiers de sauvegarde d'éditeurs
    execute_or_simulate "find . -name '*.bak' -type f -delete" "Suppression des fichiers .bak"
    execute_or_simulate "find . -name '*.tmp' -type f -delete" "Suppression des fichiers .tmp"
    execute_or_simulate "find . -name '*~' -type f -delete" "Suppression des fichiers de sauvegarde"

    # Fichiers macOS
    execute_or_simulate "find . -name '.DS_Store' -type f -delete" "Suppression des fichiers .DS_Store"

    # Fichiers Windows
    execute_or_simulate "find . -name 'Thumbs.db' -type f -delete" "Suppression des fichiers Thumbs.db"
    execute_or_simulate "find . -name 'desktop.ini' -type f -delete" "Suppression des fichiers desktop.ini"
}

# Nettoyage des dépendances
cleanup_dependencies() {
    log_info "Nettoyage des dépendances..."

    # Répertoires de dépendances inutiles
    if [ -d "node_modules" ]; then
        execute_or_simulate "rm -rf node_modules" "Suppression de node_modules"
    fi

    if [ -f "package-lock.json" ]; then
        execute_or_simulate "rm -f package-lock.json" "Suppression de package-lock.json"
    fi

    if [ -f "yarn.lock" ]; then
        execute_or_simulate "rm -f yarn.lock" "Suppression de yarn.lock"
    fi
}

# Nettoyage des fichiers de build
cleanup_build_files() {
    log_info "Nettoyage des fichiers de build..."

    if [ -d "public/build" ]; then
        execute_or_simulate "rm -rf public/build" "Suppression du répertoire public/build"
    fi

    if [ -d "assets/vendor" ]; then
        execute_or_simulate "rm -rf assets/vendor" "Suppression des assets vendor"
    fi
}

# Optimisation des autoloads Composer
optimize_composer() {
    log_info "Optimisation Composer..."

    execute_or_simulate "composer dump-autoload --optimize" "Optimisation de l'autoload Composer"
}

# Nettoyage des permissions
fix_permissions() {
    log_info "Correction des permissions..."

    execute_or_simulate "chmod -R 755 scripts/" "Correction des permissions des scripts"
    execute_or_simulate "chmod -R 777 var/" "Correction des permissions du répertoire var"

    if [ -d "public/uploads" ]; then
        execute_or_simulate "chmod -R 777 public/uploads" "Correction des permissions des uploads"
    fi
}

# Validation du code
validate_code() {
    log_info "Validation du code..."

    # Vérification de la syntaxe PHP
    execute_or_simulate "find src/ -name '*.php' -exec php -l {} \;" "Vérification de la syntaxe PHP"

    # Vérification des templates Twig (optionnelle)
    if command -v php >/dev/null 2>&1 && [ -f "bin/console" ]; then
        if php bin/console list | grep -q "lint:twig" 2>/dev/null; then
            execute_or_simulate "php bin/console lint:twig templates/ 2>/dev/null || true" "Vérification des templates Twig"
        fi
    fi

    # Vérification de la configuration YAML (optionnelle)
    if command -v php >/dev/null 2>&1 && [ -f "bin/console" ]; then
        if php bin/console list | grep -q "lint:yaml" 2>/dev/null; then
            execute_or_simulate "php bin/console lint:yaml config/ 2>/dev/null || true" "Vérification de la configuration YAML"
        fi
    fi
}

# Affichage des statistiques
show_statistics() {
    echo ""
    log_info "📊 Statistiques du code base:"
    echo ""

    echo "📁 Fichiers par type:"
    echo "  - PHP: $(find src/ -name '*.php' | wc -l) fichiers"
    echo "  - Twig: $(find templates/ -name '*.twig' | wc -l) templates"
    echo "  - YAML: $(find config/ -name '*.yaml' | wc -l) configurations"
    echo "  - Tests: $(find tests/ -name '*.php' | wc -l) tests"

    echo ""
    echo "📏 Lignes de code:"
    echo "  - PHP: $(find src/ -name '*.php' -exec wc -l {} + | tail -1 | awk '{print $1}') lignes"
    echo "  - Twig: $(find templates/ -name '*.twig' -exec wc -l {} + | tail -1 | awk '{print $1}') lignes"
    echo "  - Tests: $(find tests/ -name '*.php' -exec wc -l {} + | tail -1 | awk '{print $1}') lignes"

    echo ""
    echo "💾 Taille des répertoires:"
    echo "  - src/: $(du -sh src/ | cut -f1)"
    echo "  - templates/: $(du -sh templates/ | cut -f1)"
    echo "  - config/: $(du -sh config/ | cut -f1)"
    echo "  - vendor/: $(du -sh vendor/ | cut -f1)"
    echo "  - var/: $(du -sh var/ | cut -f1)"
}

# Fonction d'aide
show_help() {
    echo "🧹 Script de nettoyage du code base EcoTask"
    echo ""
    echo "Usage:"
    echo "  ./scripts/cleanup-codebase.sh [options]"
    echo ""
    echo "Options:"
    echo "  --dry-run    Simule les actions sans les exécuter"
    echo "  --help       Affiche cette aide"
    echo ""
    echo "Actions effectuées:"
    echo "  - Nettoyage des caches Symfony"
    echo "  - Suppression des fichiers temporaires"
    echo "  - Nettoyage des dépendances inutiles"
    echo "  - Suppression des fichiers de build"
    echo "  - Optimisation Composer"
    echo "  - Correction des permissions"
    echo "  - Validation du code"
}

# Fonction principale
main() {
    echo "🧹 Nettoyage du Code Base EcoTask"
    echo "================================="

    if [ "$DRY_RUN" = true ]; then
        log_warning "Mode simulation activé - aucune modification ne sera effectuée"
    fi

    echo ""

    cleanup_symfony_cache
    cleanup_temp_files
    cleanup_dependencies
    cleanup_build_files

    if [ "$DRY_RUN" = false ]; then
        optimize_composer
        fix_permissions
        validate_code
    fi

    show_statistics

    echo ""
    if [ "$DRY_RUN" = false ]; then
        log_success "🎉 Nettoyage du code base terminé avec succès !"
    else
        log_info "🎯 Simulation terminée - utilisez sans --dry-run pour exécuter"
    fi
}

# Gestion des arguments
case "${1:-}" in
    --dry-run)
        DRY_RUN=true
        main
        ;;
    --help|-h)
        show_help
        ;;
    "")
        main
        ;;
    *)
        log_error "Option inconnue: $1"
        show_help
        exit 1
        ;;
esac
