#!/bin/bash

# Script pour vérifier le statut des GitHub Actions
# Usage: ./scripts/check-github-actions.sh

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Fonction principale
main() {
    echo "🔍 Vérification GitHub Actions EcoTask"
    echo "======================================"
    echo ""
    
    log_info "Corrections apportées au Dockerfile :"
    echo "  ✅ Suppression des références à supervisor"
    echo "  ✅ Utilisation directe de php-fpm + nginx"
    echo "  ✅ Ajout du package symfony/asset"
    echo "  ✅ Configuration PHP-FPM améliorée"
    echo ""
    
    log_info "Changements effectués :"
    echo "  📝 Dockerfile corrigé (suppression supervisor)"
    echo "  📦 composer.json mis à jour (symfony/asset)"
    echo "  🔄 Push vers GitHub effectué"
    echo ""
    
    log_success "Le nouveau build GitHub Actions devrait maintenant réussir !"
    echo ""
    
    log_info "Pour surveiller le build :"
    echo "  🌐 Allez sur : https://github.com/sami53tk/EcoTask/actions"
    echo "  👀 Vérifiez le workflow '🚀 Déploiement'"
    echo "  ⏱️  Le build devrait prendre ~5-10 minutes"
    echo ""
    
    log_info "Étapes du build corrigé :"
    echo "  1. ✅ Checkout du code"
    echo "  2. ✅ Setup Docker Buildx"
    echo "  3. ✅ Login GitHub Container Registry"
    echo "  4. ✅ Build de l'image (sans supervisor)"
    echo "  5. ✅ Push vers ghcr.io"
    echo ""
    
    log_warning "Si le build échoue encore :"
    echo "  🔧 Vérifiez les logs GitHub Actions"
    echo "  📞 Contactez l'équipe de développement"
    echo "  🔄 Relancez le build manuellement"
    echo ""
    
    log_success "Configuration CI/CD EcoTask mise à jour avec succès !"
}

# Exécution
main "$@"
