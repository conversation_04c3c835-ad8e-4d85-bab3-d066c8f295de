#!/bin/bash

# Script d'initialisation de la base de données EcoTask
# Usage: ./scripts/init-database.sh [environment]

set -e

ENVIRONMENT=${1:-test}
PROJECT_NAME="ecotask"

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Détermination du conteneur selon l'environnement
get_container_name() {
    case "$ENVIRONMENT" in
        "test")
            echo "ecotask_app_test"
            ;;
        "dev"|"development")
            echo "ecotask_app"
            ;;
        "prod"|"production")
            echo "ecotask_app_prod"
            ;;
        *)
            log_error "Environnement non reconnu: $ENVIRONMENT"
            exit 1
            ;;
    esac
}

# Vérification que le conteneur est en cours d'exécution
check_container() {
    local container_name=$1
    
    if ! sudo docker ps | grep -q "$container_name"; then
        log_error "Le conteneur $container_name n'est pas en cours d'exécution"
        log_info "Démarrez d'abord les services avec:"
        case "$ENVIRONMENT" in
            "test")
                echo "  sudo docker-compose -f docker-compose.test.yml up -d"
                ;;
            "dev"|"development")
                echo "  sudo docker-compose up -d"
                ;;
            "prod"|"production")
                echo "  sudo docker-compose -f docker-compose.prod.yml up -d"
                ;;
        esac
        exit 1
    fi
    
    log_success "Conteneur $container_name trouvé et en cours d'exécution"
}

# Attendre que la base de données soit prête
wait_for_database() {
    local container_name=$1
    local max_attempts=30
    local attempt=1
    
    log_info "Attente de la disponibilité de la base de données..."
    
    while [ $attempt -le $max_attempts ]; do
        if sudo docker exec "$container_name" php bin/console doctrine:query:sql "SELECT 1" >/dev/null 2>&1; then
            log_success "Base de données disponible"
            return 0
        fi
        
        log_info "Tentative $attempt/$max_attempts - Base de données non disponible, attente..."
        sleep 2
        ((attempt++))
    done
    
    log_error "La base de données n'est pas disponible après $max_attempts tentatives"
    exit 1
}

# Création de la base de données
create_database() {
    local container_name=$1
    
    log_info "Création de la base de données..."
    
    if sudo docker exec "$container_name" php bin/console doctrine:database:create --if-not-exists; then
        log_success "Base de données créée ou existe déjà"
    else
        log_error "Échec de la création de la base de données"
        exit 1
    fi
}

# Exécution des migrations
run_migrations() {
    local container_name=$1
    
    log_info "Exécution des migrations..."
    
    if sudo docker exec "$container_name" php bin/console doctrine:migrations:migrate --no-interaction; then
        log_success "Migrations exécutées avec succès"
    else
        log_error "Échec des migrations"
        exit 1
    fi
}

# Chargement des fixtures (seulement en dev/test)
load_fixtures() {
    local container_name=$1
    
    if [ "$ENVIRONMENT" = "prod" ] || [ "$ENVIRONMENT" = "production" ]; then
        log_warning "Fixtures ignorées en production"
        return 0
    fi
    
    log_info "Chargement des fixtures..."
    
    if sudo docker exec "$container_name" php bin/console doctrine:fixtures:load --no-interaction; then
        log_success "Fixtures chargées avec succès"
    else
        log_error "Échec du chargement des fixtures"
        exit 1
    fi
}

# Vérification de la santé de l'application
check_health() {
    log_info "Vérification de la santé de l'application..."
    
    local health_url="http://localhost:8080/health"
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$health_url" | grep -q '"status":"healthy"'; then
            log_success "Application en bonne santé"
            return 0
        fi
        
        log_info "Tentative $attempt/$max_attempts - Application non prête, attente..."
        sleep 3
        ((attempt++))
    done
    
    log_warning "L'application ne répond pas correctement au health check"
    log_info "Vérifiez manuellement: $health_url"
}

# Affichage des informations de connexion
show_connection_info() {
    echo ""
    log_success "🎉 Initialisation terminée avec succès !"
    echo ""
    echo "📊 Informations de connexion:"
    echo "  - Application: http://localhost:8080"
    echo "  - API Santé: http://localhost:8080/health"
    
    if [ "$ENVIRONMENT" != "prod" ] && [ "$ENVIRONMENT" != "production" ]; then
        echo "  - Adminer: http://localhost:8081"
        echo "  - MailHog: http://localhost:8025"
    fi
    
    echo ""
    echo "🗄️ Base de données initialisée avec:"
    echo "  - Tables créées via les migrations"
    
    if [ "$ENVIRONMENT" != "prod" ] && [ "$ENVIRONMENT" != "production" ]; then
        echo "  - Données de test chargées (fixtures)"
    fi
    
    echo ""
}

# Fonction principale
main() {
    echo "🚀 Initialisation de la base de données EcoTask"
    echo "Environnement: $ENVIRONMENT"
    echo "=============================================="
    
    local container_name
    container_name=$(get_container_name)
    
    check_container "$container_name"
    wait_for_database "$container_name"
    create_database "$container_name"
    run_migrations "$container_name"
    load_fixtures "$container_name"
    check_health
    show_connection_info
}

# Fonction d'aide
show_help() {
    echo "🗄️ Script d'initialisation de la base de données EcoTask"
    echo ""
    echo "Usage:"
    echo "  ./scripts/init-database.sh [environment]"
    echo ""
    echo "Environnements disponibles:"
    echo "  test        Configuration de test (défaut)"
    echo "  dev         Configuration de développement"
    echo "  prod        Configuration de production"
    echo ""
    echo "Exemples:"
    echo "  ./scripts/init-database.sh test"
    echo "  ./scripts/init-database.sh dev"
    echo "  ./scripts/init-database.sh prod"
}

# Gestion des arguments
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    ""|test|dev|development|prod|production)
        main
        ;;
    *)
        log_error "Environnement non reconnu: $1"
        show_help
        exit 1
        ;;
esac
