{% extends 'base.html.twig' %}

{% block title %}Nouveau projet - EcoTask{% endblock %}

{% block body %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-plus-circle"></i> Nouveau projet</h1>
            <a href="{{ path('app_project_index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Retour à la liste
            </a>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Informations du projet</h5>
            </div>
            <div class="card-body">
                {{ form_start(form) }}
                
                <div class="mb-3">
                    {{ form_label(form.name) }}
                    {{ form_widget(form.name) }}
                    {{ form_errors(form.name) }}
                </div>

                <div class="mb-3">
                    {{ form_label(form.description) }}
                    {{ form_widget(form.description) }}
                    {{ form_errors(form.description) }}
                </div>

                <div class="mb-3">
                    {{ form_label(form.members) }}
                    {{ form_widget(form.members) }}
                    {{ form_errors(form.members) }}
                    {{ form_help(form.members) }}
                </div>

                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> À propos des projets EcoTask</h6>
                    <p class="mb-0">
                        Chaque projet permet de regrouper des tâches et de suivre leur impact environnemental.
                        Vous pourrez voir les émissions CO₂ totales du projet et analyser l'efficacité énergétique de votre équipe.
                    </p>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ path('app_project_index') }}" class="btn btn-secondary">
                        <i class="bi bi-x"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check"></i> Créer le projet
                    </button>
                </div>

                {{ form_end(form) }}
            </div>
        </div>
    </div>
</div>
{% endblock %}
